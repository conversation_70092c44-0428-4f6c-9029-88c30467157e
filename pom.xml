<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.5.3</version>
		<relativePath />
		<!-- lookup parent from repository -->
	</parent>
	<groupId>com.github.cret.web</groupId>
	<artifactId>web-parent</artifactId>
	<version>0.0.1</version>
	<packaging>pom</packaging>
	<modules>
		<module>web-system</module>
		<module>web-security</module>
		<module>web-common</module>
		<module>line-load</module>
	</modules>
	<name>web-parent</name>
	<description>Line-load project for Spring Boot</description>
	<url />
	<licenses>
		<license />
	</licenses>
	<developers>
		<developer />
	</developers>
	<scm>
		<connection />
		<developerConnection />
		<tag />
		<url />
	</scm>
	<properties>
		<java.version>17</java.version>
		<project.version>0.1</project.version>
		<org.mapstruct.version>1.6.3</org.mapstruct.version>
		<!-- grpc -->
		<protobuf.version>3.25.1</protobuf.version>
		<protobuf-plugin.version>0.6.1</protobuf-plugin.version>
		<grpc.version>1.58.0</grpc.version>
		<spring.boot.version>3.5.3</spring.boot.version>
	</properties>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-bom</artifactId>
				<version>5.8.33</version>
				<type>pom</type>
				<!-- 注意这里是import -->
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>net.logstash.logback</groupId>
				<artifactId>logstash-logback-encoder</artifactId>
				<version>7.4</version>
			</dependency>
			<dependency>
				<groupId>jakarta.validation</groupId>
				<artifactId>jakarta.validation-api</artifactId>
				<version>3.1.1</version>
			</dependency>
			<dependency>
				<groupId>com.github.cret.web</groupId>
				<artifactId>web-system</artifactId>
				<version>0.0.1</version>
			</dependency>
			<dependency>
				<groupId>com.github.cret.web</groupId>
				<artifactId>web-security</artifactId>
				<version>0.0.1</version>
			</dependency>
			<dependency>
				<groupId>com.github.cret.web</groupId>
				<artifactId>web-common</artifactId>
				<version>0.0.1</version>
			</dependency>
			<dependency>
				<groupId>io.minio</groupId>
				<artifactId>minio</artifactId>
				<version>8.5.17</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/org.casbin/casdoor-java-sdk -->
			<dependency>
				<groupId>org.casbin</groupId>
				<artifactId>casdoor-java-sdk</artifactId>
				<version>1.34.0</version>
			</dependency>
			<dependency>
				<groupId>org.mapstruct</groupId>
				<artifactId>mapstruct</artifactId>
				<version>${org.mapstruct.version}</version>
			</dependency>
			<dependency>
				<groupId>io.jsonwebtoken</groupId>
				<artifactId>jjwt-api</artifactId>
				<version>0.12.6</version>
			</dependency>
			<dependency>
				<groupId>io.jsonwebtoken</groupId>
				<artifactId>jjwt-impl</artifactId>
				<version>0.12.6</version>
			</dependency>
			<dependency>
				<groupId>io.jsonwebtoken</groupId>
				<artifactId>jjwt-jackson</artifactId>
				<!-- or jjwt-gson if Gson is preferred -->
				<version>0.12.6</version>
			</dependency>
			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-bom</artifactId>
				<version>5.8.37</version>
			</dependency>
			<!-- Apache POI for Excel parsing -->
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi</artifactId>
				<version>5.2.4</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml</artifactId>
				<version>5.2.4</version>
			</dependency>
			<!-- grpc -->
			<dependency>
				<groupId>io.grpc</groupId>
				<artifactId>grpc-stub</artifactId>
				<version>${grpc.version}</version>
			</dependency>
			<dependency>
				<groupId>io.grpc</groupId>
				<artifactId>grpc-protobuf</artifactId>
				<version>${grpc.version}</version>
			</dependency>
			<dependency>
				<groupId>io.grpc</groupId>
				<artifactId>grpc-netty</artifactId>
				<version>${grpc.version}</version>
			</dependency>
			<dependency>
				<groupId>io.grpc</groupId>
				<artifactId>grpc-services</artifactId>
				<version>${grpc.version}</version>
			</dependency>
			<!-- Add annotation for Java 9+ -->
			<dependency>
				<groupId>org.apache.tomcat</groupId>
				<artifactId>annotations-api</artifactId>
				<version>6.0.53</version>
				<scope>provided</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>runtime</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<source>17</source>
					<!-- depending on your project -->
					<target>17</target>
					<!-- depending on your project -->
					<annotationProcessorPaths>
						<path>
							<groupId>org.mapstruct</groupId>
							<artifactId>mapstruct-processor</artifactId>
							<version>${org.mapstruct.version}</version>
						</path>
						<path>
							<groupId>org.springframework.boot</groupId>
							<artifactId>spring-boot-configuration-processor</artifactId>
							<version>${spring.boot.version}</version>
						</path>
						<!-- other annotation processors -->
					</annotationProcessorPaths>
				</configuration>
			</plugin>
			<!-- protobuf-maven-plugin -->
			<plugin>
				<groupId>org.xolstice.maven.plugins</groupId>
				<artifactId>protobuf-maven-plugin</artifactId>
				<version>${protobuf-plugin.version}</version>
				<configuration>
					<protocArtifact>com.google.protobuf:protoc:${protobuf.version}:exe:${os.detected.classifier}
					</protocArtifact>
					<pluginId>grpc-java</pluginId>
					<pluginArtifact>io.grpc:protoc-gen-grpc-java:${grpc.version}:exe:${os.detected.classifier}
					</pluginArtifact>
					<!--默认值-->
					<protoSourceRoot>${project.basedir}/line-load/src/main/proto</protoSourceRoot>
					<outputDirectory>${project.basedir}/line-load/src/main/java</outputDirectory>
					<!--设置是否在生成java文件之前清空outputDirectory的文件，默认值为true，设置为false时也会覆盖同名文件-->
					<clearOutputDirectory>false</clearOutputDirectory>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>compile</goal>
							<goal>compile-custom</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>