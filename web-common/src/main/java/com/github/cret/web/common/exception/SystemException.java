package com.github.cret.web.common.exception;

import com.github.cret.web.common.domain.ErrRes;
import com.github.cret.web.common.enumerate.SysErrEnum;
import org.springframework.http.HttpStatus;

public class SystemException extends RuntimeException {
    private final SysErrEnum err;

    public SystemException(SysErrEnum err) {
        super(err.getMsg());
        this.err = err;
    }

    public SystemException(SysErrEnum err, String msg) {
        super(msg);
        this.err = err;
    }

    public ErrRes toRes() {
        return new ErrRes(err.getCode(), getMessage());
    }

    public String getCode() {
        return err.getCode();
    }


    public HttpStatus getStatus() {
        return err.getHttpStatus();
    }

}
