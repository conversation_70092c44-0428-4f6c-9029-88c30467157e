package com.github.cret.web.common.domain;

import java.util.List;

import org.springframework.data.domain.Page;

public class PageList<T> {
    private Long total;
    private Boolean hasNext;
    private List<T> list;

    public PageList() {
    }

    public static <T> PageList<T> fromPage(Page<T> page) {
        PageList<T> pageList = new PageList<>();
        pageList.total = page.getTotalElements();
        pageList.list = page.toList();
        pageList.hasNext = page.hasNext();
        return pageList;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Boolean getHasNext() {
        return hasNext;
    }

    public void setHasNext(Boolean hasNext) {
        this.hasNext = hasNext;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }
}
