package com.github.cret.web.common.component;

import com.github.cret.web.common.domain.ErrRes;
import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.common.exception.SystemException;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.NoSuchElementException;

@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(SystemException.class)
    public ResponseEntity<ErrRes> handleSystemException(SystemException ex) {
        return new ResponseEntity<>(
                ex.toRes(),
                ex.getStatus()
        );
    }

    @ExceptionHandler(NoSuchElementException.class)
    public ResponseEntity<ErrRes> handleNoSuchElementException(NoSuchElementException ex) {
        return new ResponseEntity<ErrRes>(SysErrEnum.NOT_FOUND.exception(ex).toRes(),
                SysErrEnum.NOT_FOUND.getHttpStatus());
    }
}
