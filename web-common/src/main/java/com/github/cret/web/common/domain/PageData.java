package com.github.cret.web.common.domain;

import org.springframework.data.domain.PageRequest;

public class PageData {

    private Integer pageNumber;
    private Integer pageSize;

    public PageData() {
        pageNumber = 1;
        pageSize = 10;
    }

    public PageRequest toPageRequest() {
        return PageRequest.of(pageNumber, pageSize);
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
