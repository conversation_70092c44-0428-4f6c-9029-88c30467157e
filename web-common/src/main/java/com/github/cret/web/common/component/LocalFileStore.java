package com.github.cret.web.common.component;

import java.io.IOException;
import java.nio.file.FileVisitResult;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.SimpleFileVisitor;
import java.nio.file.StandardOpenOption;
import java.nio.file.attribute.BasicFileAttributes;

import org.springframework.core.io.FileSystemResource;
import org.springframework.web.multipart.MultipartFile;

import com.github.cret.web.common.domain.DownloadFile;
import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.common.exception.SystemException;

public class LocalFileStore implements FileStore {

    private final String FILE_STORE_DIR = "files";

    @Override
    public void set(String key, MultipartFile file) throws IOException {
        Path filePath = Paths.get(FILE_STORE_DIR, key);
        Path dirPath = filePath.getParent();
        if (!Files.exists(dirPath)) {
            Files.createDirectories(dirPath);
        }
        Files.write(filePath, file.getBytes(), StandardOpenOption.CREATE_NEW);
    }

    @Override
    public DownloadFile get(String key) throws IOException {
        Path filePath = Paths.get(FILE_STORE_DIR, key);
        if (Files.exists(filePath)) {
            FileSystemResource resource = new FileSystemResource(filePath);
            String contentType = Files.probeContentType(filePath);
            return new DownloadFile(resource, contentType, resource.getFilename());
        }
        throw new SystemException(SysErrEnum.NOT_FOUND);
    }

    @Override
    public void del(String key) throws IOException {
        Path dir = Paths.get(FILE_STORE_DIR, key).getParent();
        Files.walkFileTree(dir, new SimpleFileVisitor<>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                Files.delete(file);
                return FileVisitResult.CONTINUE;
            }

            @Override
            public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                Files.delete(dir);
                return FileVisitResult.CONTINUE;
            }
        });

    }

}
