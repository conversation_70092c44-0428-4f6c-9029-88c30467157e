package com.github.cret.web.common.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.github.cret.web.common.component.FileStore;
import com.github.cret.web.common.component.LocalFileStore;

@Configuration
public class FileStoreConfig {

    @Bean
    @ConditionalOnProperty(name = "file-store.minio.endpoint")
    public FileStore minioFileStore(MinioFileStoreProperties properties) {
        return new com.github.cret.web.common.component.MinioFileStore(properties);
    }

    @Bean
    @ConditionalOnMissingBean(FileStore.class)
    public FileStore localFileStore() {
        return new LocalFileStore();
    }

}
