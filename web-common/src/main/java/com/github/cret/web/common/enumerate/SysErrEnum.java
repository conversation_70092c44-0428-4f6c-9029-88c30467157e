package com.github.cret.web.common.enumerate;

import org.springframework.http.HttpStatus;

import com.github.cret.web.common.exception.SystemException;

public enum SysErrEnum {

    NOT_FOUND("10404", "不存在的对象", HttpStatus.NOT_FOUND),
    UNKNOWN_ERROR("19999", "未知错误", HttpStatus.INTERNAL_SERVER_ERROR),
    UNAUTHORIZED("10401", "未登录", HttpStatus.UNAUTHORIZED),
    NETWORK_ERROR("10503", "网络错误", HttpStatus.SERVICE_UNAVAILABLE),
    INTERNAL_SERVER_ERROR("50000", "系统内部错误", HttpStatus.INTERNAL_SERVER_ERROR),

    LOGIN_FAIL("20002", "登录失败", HttpStatus.BAD_REQUEST),
    CONFLICT_KEY("20001", "唯一属性冲突", HttpStatus.BAD_REQUEST),
    UPLOAD_FILE_FAIL("20004", "上传文件失败", HttpStatus.BAD_REQUEST),
    UPLOAD_FILE_CHECK_FAIL("20005", "上传文件内存检查", HttpStatus.BAD_REQUEST),
    UNACCEPTABLE_OPERATION("20003", "不允许的操作", HttpStatus.UNPROCESSABLE_ENTITY);

    SysErrEnum(String code, String msg, HttpStatus status) {
        this.code = code;
        this.msg = msg;
        this.status = status;
    }

    private final String code;
    private final String msg;
    private final HttpStatus status;

    public SystemException exception() {
        return new SystemException(this);
    }

    public SystemException exception(String message) {
        return new SystemException(this, message);
    }

    public SystemException exception(String message, Exception e) {
        return new SystemException(this, message);
    }

    public SystemException exception(Exception e) {
        return new SystemException(this, e.getMessage());
    }

    public String getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }

    public HttpStatus getHttpStatus() {
        return this.status;
    }
}
