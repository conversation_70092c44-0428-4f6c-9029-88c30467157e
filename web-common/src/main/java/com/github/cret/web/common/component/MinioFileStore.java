package com.github.cret.web.common.component;

import java.io.IOException;

import org.springframework.core.io.InputStreamResource;
import org.springframework.web.multipart.MultipartFile;

import com.github.cret.web.common.config.MinioFileStoreProperties;
import com.github.cret.web.common.domain.DownloadFile;

import io.minio.BucketExistsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.RemoveObjectArgs;

public class MinioFileStore implements FileStore {

    private final MinioClient minioClient;
    private final String bucketName;

    public MinioFileStore(MinioFileStoreProperties properties) {
        this.minioClient = MinioClient.builder()
                .endpoint(properties.getEndpoint())
                .credentials(properties.getAccessKey(), properties.getSecretKey())
                .build();
        this.bucketName = properties.getBucketName();
        try {
            if (!minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build())) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize Minio bucket: " + bucketName, e);
        }
    }

    @Override
    public void set(String key, MultipartFile file) throws IOException {
        try {
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(this.bucketName)
                    .contentType(file.getContentType())
                    .object(key)
                    .stream(file.getInputStream(), file.getSize(), -1)
                    .build());
        } catch (Exception ex) {
            throw new IOException("Failed to upload file to Minio: " + ex.getMessage(), ex);
        }
    }

    @Override
    public DownloadFile get(String key) throws IOException {
        try {
            var stat = minioClient.statObject(
                    io.minio.StatObjectArgs.builder()
                            .bucket(this.bucketName)
                            .object(key)
                            .build());
            var res = minioClient.getObject(
                    io.minio.GetObjectArgs.builder()
                            .bucket(this.bucketName)
                            .object(key)
                            .build());

            return new DownloadFile(new InputStreamResource(res), stat.contentType(), key);
        } catch (Exception ex) {
            throw new IOException("Failed to retrieve file from Minio: " + ex.getMessage(), ex);
        }
    }

    @Override
    public void del(String key) throws IOException {
        try {
            minioClient.removeObject(RemoveObjectArgs.builder()
                    .bucket(bucketName)
                    .object(key)
                    .build());
        } catch (Exception ex) {
            throw new IOException("Failed to retrieve file from Minio: " + ex.getMessage(), ex);
        }
    }

}
