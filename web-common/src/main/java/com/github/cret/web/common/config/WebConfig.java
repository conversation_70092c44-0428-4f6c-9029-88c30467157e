package com.github.cret.web.common.config;

import com.github.cret.web.common.component.CustomDefaultErrorAttributes;
import org.springframework.boot.web.servlet.error.DefaultErrorAttributes;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class WebConfig {
    @Bean
    public DefaultErrorAttributes errorAttributes() {
        return new CustomDefaultErrorAttributes();
    }
}



