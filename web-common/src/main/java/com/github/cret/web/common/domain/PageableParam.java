package com.github.cret.web.common.domain;

import org.springframework.data.domain.PageRequest;

public class PageableParam<T> {

    private PageData pageData;

    private T searchParams;

    public PageableParam() {
    }

    public PageData getPageData() {
        return pageData;
    }

    public void setPageData(PageData pageData) {
        this.pageData = pageData;
    }

    public T getSearchParams() {
        return searchParams;
    }

    public void setSearchParams(T searchParams) {
        this.searchParams = searchParams;
    }

    public PageRequest getPageRequest() {
        return pageData.toPageRequest();
    }
}
