# --- Stage 1: Build Stage ---
# 使用包含 JDK 和 Maven 的基础镜像
# 推荐使用 Eclipse Temurin 或 OpenJDK 官方镜像，它们有 Maven 版本
FROM maven:3.9.6-eclipse-temurin-17-alpine AS builder

# 设置工作目录，这是你的项目源码将复制到的地方
WORKDIR /app

# 复制 Maven 的 pom.xml 文件和可能的其他项目配置文件
# 这一步是缓存层优化的关键：如果pom.xml没有变化，Maven依赖将不会重新下载
COPY pom.xml .
COPY web-system/pom.xml ./web-system/
COPY web-common/pom.xml ./web-common/
COPY web-security/pom.xml ./web-security/
COPY line-load/pom.xml ./line-load/
# 如果你有父pom或者多模块项目，需要复制所有相关的pom文件
# COPY pom.xml ./
# COPY your-parent-module/pom.xml ./your-parent-module/
# ...以此类推复制所有模块的pom.xml

# 下载项目依赖，这一步可以被Docker缓存，如果pom.xml没有变化则跳过
RUN mvn dependency:go-offline -B

# 复制项目的所有源代码
# 这将在上一步下载依赖后进行，以利用Docker的缓存机制
COPY . .

# 执行 Maven 打包命令
# -DskipTests 跳过测试
# -B 批处理模式，避免交互
# --file ./pom.xml 如果pom.xml不在WORKDIR的根目录，需要指定
RUN mvn package -DskipTests -B

# --- Stage 2: Run Stage ---
# 使用更小的JRE镜像来运行应用程序，减少最终镜像大小
FROM openjdk:17-slim-buster

# 设置时区为中国标准时间
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 从构建阶段复制打包好的 JAR 文件
# 注意这里假设你的jar包生成在 target/ 目录下，并且命名是 line_load-1.0-SNAPSHOT.jar
# 请根据你实际的Maven项目名称和版本号调整
COPY --from=builder /app/line-load/target/line-load-1.0-SNAPSHOT.jar /usr/local/line_load.jar

# 设置工作目录为应用程序所在的目录
WORKDIR /usr/local

VOLUME [ "/usr/local/logs" ]
# 暴露应用程序监听的端口
EXPOSE 8091/tcp

# 在Java启动参数中设置时区，并启动Spring Boot应用程序
# --spring.profiles.active=prod 设置Spring Profile为生产环境
CMD ["java", "-Duser.timezone=Asia/Shanghai", "-jar", "line_load.jar", "--spring.profiles.active=prod"]