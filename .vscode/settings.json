{
    "java.compile.nullAnalysis.mode": "automatic",
    "java.configuration.updateBuildConfiguration": "automatic",
    "formatFiles.excludePattern": "*.class",
    "replacerules.rules": {
        "controller": {
            "find": "(.+) (.+)\\((.+);",
            "replace": "@RequestMapping(\"/$2\")  public $1 $2 ($3{\n}",
            "languages": [
                "java"
            ]
        }
    },
}