// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: IntelligentSchedule.proto

// Protobuf Java Version: 3.25.1
package com.github.cret.web.lineLoad.grpc.generate;

/**
 * Protobuf type {@code com.hongjingwh.lp.Plan}
 */
public final class Plan extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.hongjingwh.lp.Plan)
    PlanOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Plan.newBuilder() to construct.
  private Plan(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Plan() {
    partNumber_ = "";
    productName_ = "";
    line_ = "";
    orderPeriod_ = "";
    productionPeriod_ = "";
    costTime_ = "";
    model_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Plan();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.internal_static_com_hongjingwh_lp_Plan_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.internal_static_com_hongjingwh_lp_Plan_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.github.cret.web.lineLoad.grpc.generate.Plan.class, com.github.cret.web.lineLoad.grpc.generate.Plan.Builder.class);
  }

  public static final int PART_NUMBER_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object partNumber_ = "";
  /**
   * <pre>
   *产品物料编码
   * </pre>
   *
   * <code>string part_number = 1;</code>
   * @return The partNumber.
   */
  @java.lang.Override
  public java.lang.String getPartNumber() {
    java.lang.Object ref = partNumber_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      partNumber_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *产品物料编码
   * </pre>
   *
   * <code>string part_number = 1;</code>
   * @return The bytes for partNumber.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPartNumberBytes() {
    java.lang.Object ref = partNumber_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      partNumber_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PRODUCT_NAME_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object productName_ = "";
  /**
   * <pre>
   *产品名称
   * </pre>
   *
   * <code>string product_name = 2;</code>
   * @return The productName.
   */
  @java.lang.Override
  public java.lang.String getProductName() {
    java.lang.Object ref = productName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      productName_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *产品名称
   * </pre>
   *
   * <code>string product_name = 2;</code>
   * @return The bytes for productName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getProductNameBytes() {
    java.lang.Object ref = productName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      productName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LINE_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object line_ = "";
  /**
   * <pre>
   *产线编号
   * </pre>
   *
   * <code>string line = 3;</code>
   * @return The line.
   */
  @java.lang.Override
  public java.lang.String getLine() {
    java.lang.Object ref = line_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      line_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *产线编号
   * </pre>
   *
   * <code>string line = 3;</code>
   * @return The bytes for line.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLineBytes() {
    java.lang.Object ref = line_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      line_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ORDER_PERIOD_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object orderPeriod_ = "";
  /**
   * <pre>
   *订单周期（年月或周） eg. 2025-01
   * </pre>
   *
   * <code>string order_period = 4;</code>
   * @return The orderPeriod.
   */
  @java.lang.Override
  public java.lang.String getOrderPeriod() {
    java.lang.Object ref = orderPeriod_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      orderPeriod_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *订单周期（年月或周） eg. 2025-01
   * </pre>
   *
   * <code>string order_period = 4;</code>
   * @return The bytes for orderPeriod.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getOrderPeriodBytes() {
    java.lang.Object ref = orderPeriod_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      orderPeriod_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PRODUCTION_PERIOD_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object productionPeriod_ = "";
  /**
   * <pre>
   *排产所在周期(年月或周) eg. 2025-01
   * </pre>
   *
   * <code>string production_period = 5;</code>
   * @return The productionPeriod.
   */
  @java.lang.Override
  public java.lang.String getProductionPeriod() {
    java.lang.Object ref = productionPeriod_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      productionPeriod_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *排产所在周期(年月或周) eg. 2025-01
   * </pre>
   *
   * <code>string production_period = 5;</code>
   * @return The bytes for productionPeriod.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getProductionPeriodBytes() {
    java.lang.Object ref = productionPeriod_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      productionPeriod_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int QUANTITY_FIELD_NUMBER = 6;
  private int quantity_ = 0;
  /**
   * <pre>
   *生产数量
   * </pre>
   *
   * <code>int32 quantity = 6;</code>
   * @return The quantity.
   */
  @java.lang.Override
  public int getQuantity() {
    return quantity_;
  }

  public static final int COST_TIME_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object costTime_ = "";
  /**
   * <pre>
   *消耗时间
   * </pre>
   *
   * <code>string cost_time = 7;</code>
   * @return The costTime.
   */
  @java.lang.Override
  public java.lang.String getCostTime() {
    java.lang.Object ref = costTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      costTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *消耗时间
   * </pre>
   *
   * <code>string cost_time = 7;</code>
   * @return The bytes for costTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCostTimeBytes() {
    java.lang.Object ref = costTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      costTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MODEL_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private volatile java.lang.Object model_ = "";
  /**
   * <pre>
   *平台
   * </pre>
   *
   * <code>string model = 8;</code>
   * @return The model.
   */
  @java.lang.Override
  public java.lang.String getModel() {
    java.lang.Object ref = model_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      model_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *平台
   * </pre>
   *
   * <code>string model = 8;</code>
   * @return The bytes for model.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getModelBytes() {
    java.lang.Object ref = model_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      model_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(partNumber_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, partNumber_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(productName_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, productName_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(line_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, line_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(orderPeriod_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, orderPeriod_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(productionPeriod_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, productionPeriod_);
    }
    if (quantity_ != 0) {
      output.writeInt32(6, quantity_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(costTime_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, costTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(model_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, model_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(partNumber_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, partNumber_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(productName_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, productName_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(line_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, line_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(orderPeriod_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, orderPeriod_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(productionPeriod_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, productionPeriod_);
    }
    if (quantity_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, quantity_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(costTime_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, costTime_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(model_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, model_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.github.cret.web.lineLoad.grpc.generate.Plan)) {
      return super.equals(obj);
    }
    com.github.cret.web.lineLoad.grpc.generate.Plan other = (com.github.cret.web.lineLoad.grpc.generate.Plan) obj;

    if (!getPartNumber()
        .equals(other.getPartNumber())) return false;
    if (!getProductName()
        .equals(other.getProductName())) return false;
    if (!getLine()
        .equals(other.getLine())) return false;
    if (!getOrderPeriod()
        .equals(other.getOrderPeriod())) return false;
    if (!getProductionPeriod()
        .equals(other.getProductionPeriod())) return false;
    if (getQuantity()
        != other.getQuantity()) return false;
    if (!getCostTime()
        .equals(other.getCostTime())) return false;
    if (!getModel()
        .equals(other.getModel())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + PART_NUMBER_FIELD_NUMBER;
    hash = (53 * hash) + getPartNumber().hashCode();
    hash = (37 * hash) + PRODUCT_NAME_FIELD_NUMBER;
    hash = (53 * hash) + getProductName().hashCode();
    hash = (37 * hash) + LINE_FIELD_NUMBER;
    hash = (53 * hash) + getLine().hashCode();
    hash = (37 * hash) + ORDER_PERIOD_FIELD_NUMBER;
    hash = (53 * hash) + getOrderPeriod().hashCode();
    hash = (37 * hash) + PRODUCTION_PERIOD_FIELD_NUMBER;
    hash = (53 * hash) + getProductionPeriod().hashCode();
    hash = (37 * hash) + QUANTITY_FIELD_NUMBER;
    hash = (53 * hash) + getQuantity();
    hash = (37 * hash) + COST_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getCostTime().hashCode();
    hash = (37 * hash) + MODEL_FIELD_NUMBER;
    hash = (53 * hash) + getModel().hashCode();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.github.cret.web.lineLoad.grpc.generate.Plan parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.Plan parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.Plan parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.Plan parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.Plan parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.Plan parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.Plan parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.Plan parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.github.cret.web.lineLoad.grpc.generate.Plan parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.github.cret.web.lineLoad.grpc.generate.Plan parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.Plan parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.Plan parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.github.cret.web.lineLoad.grpc.generate.Plan prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.hongjingwh.lp.Plan}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.hongjingwh.lp.Plan)
      com.github.cret.web.lineLoad.grpc.generate.PlanOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.internal_static_com_hongjingwh_lp_Plan_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.internal_static_com_hongjingwh_lp_Plan_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.github.cret.web.lineLoad.grpc.generate.Plan.class, com.github.cret.web.lineLoad.grpc.generate.Plan.Builder.class);
    }

    // Construct using com.github.cret.web.lineLoad.grpc.generate.Plan.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      partNumber_ = "";
      productName_ = "";
      line_ = "";
      orderPeriod_ = "";
      productionPeriod_ = "";
      quantity_ = 0;
      costTime_ = "";
      model_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.internal_static_com_hongjingwh_lp_Plan_descriptor;
    }

    @java.lang.Override
    public com.github.cret.web.lineLoad.grpc.generate.Plan getDefaultInstanceForType() {
      return com.github.cret.web.lineLoad.grpc.generate.Plan.getDefaultInstance();
    }

    @java.lang.Override
    public com.github.cret.web.lineLoad.grpc.generate.Plan build() {
      com.github.cret.web.lineLoad.grpc.generate.Plan result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.github.cret.web.lineLoad.grpc.generate.Plan buildPartial() {
      com.github.cret.web.lineLoad.grpc.generate.Plan result = new com.github.cret.web.lineLoad.grpc.generate.Plan(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.github.cret.web.lineLoad.grpc.generate.Plan result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.partNumber_ = partNumber_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.productName_ = productName_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.line_ = line_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.orderPeriod_ = orderPeriod_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.productionPeriod_ = productionPeriod_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.quantity_ = quantity_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.costTime_ = costTime_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.model_ = model_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.github.cret.web.lineLoad.grpc.generate.Plan) {
        return mergeFrom((com.github.cret.web.lineLoad.grpc.generate.Plan)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.github.cret.web.lineLoad.grpc.generate.Plan other) {
      if (other == com.github.cret.web.lineLoad.grpc.generate.Plan.getDefaultInstance()) return this;
      if (!other.getPartNumber().isEmpty()) {
        partNumber_ = other.partNumber_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getProductName().isEmpty()) {
        productName_ = other.productName_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (!other.getLine().isEmpty()) {
        line_ = other.line_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.getOrderPeriod().isEmpty()) {
        orderPeriod_ = other.orderPeriod_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (!other.getProductionPeriod().isEmpty()) {
        productionPeriod_ = other.productionPeriod_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (other.getQuantity() != 0) {
        setQuantity(other.getQuantity());
      }
      if (!other.getCostTime().isEmpty()) {
        costTime_ = other.costTime_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (!other.getModel().isEmpty()) {
        model_ = other.model_;
        bitField0_ |= 0x00000080;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              partNumber_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              productName_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              line_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              orderPeriod_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              productionPeriod_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 48: {
              quantity_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 58: {
              costTime_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 66: {
              model_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object partNumber_ = "";
    /**
     * <pre>
     *产品物料编码
     * </pre>
     *
     * <code>string part_number = 1;</code>
     * @return The partNumber.
     */
    public java.lang.String getPartNumber() {
      java.lang.Object ref = partNumber_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        partNumber_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *产品物料编码
     * </pre>
     *
     * <code>string part_number = 1;</code>
     * @return The bytes for partNumber.
     */
    public com.google.protobuf.ByteString
        getPartNumberBytes() {
      java.lang.Object ref = partNumber_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        partNumber_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *产品物料编码
     * </pre>
     *
     * <code>string part_number = 1;</code>
     * @param value The partNumber to set.
     * @return This builder for chaining.
     */
    public Builder setPartNumber(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      partNumber_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *产品物料编码
     * </pre>
     *
     * <code>string part_number = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearPartNumber() {
      partNumber_ = getDefaultInstance().getPartNumber();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *产品物料编码
     * </pre>
     *
     * <code>string part_number = 1;</code>
     * @param value The bytes for partNumber to set.
     * @return This builder for chaining.
     */
    public Builder setPartNumberBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      partNumber_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object productName_ = "";
    /**
     * <pre>
     *产品名称
     * </pre>
     *
     * <code>string product_name = 2;</code>
     * @return The productName.
     */
    public java.lang.String getProductName() {
      java.lang.Object ref = productName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        productName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *产品名称
     * </pre>
     *
     * <code>string product_name = 2;</code>
     * @return The bytes for productName.
     */
    public com.google.protobuf.ByteString
        getProductNameBytes() {
      java.lang.Object ref = productName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        productName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *产品名称
     * </pre>
     *
     * <code>string product_name = 2;</code>
     * @param value The productName to set.
     * @return This builder for chaining.
     */
    public Builder setProductName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      productName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *产品名称
     * </pre>
     *
     * <code>string product_name = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearProductName() {
      productName_ = getDefaultInstance().getProductName();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *产品名称
     * </pre>
     *
     * <code>string product_name = 2;</code>
     * @param value The bytes for productName to set.
     * @return This builder for chaining.
     */
    public Builder setProductNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      productName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.lang.Object line_ = "";
    /**
     * <pre>
     *产线编号
     * </pre>
     *
     * <code>string line = 3;</code>
     * @return The line.
     */
    public java.lang.String getLine() {
      java.lang.Object ref = line_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        line_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *产线编号
     * </pre>
     *
     * <code>string line = 3;</code>
     * @return The bytes for line.
     */
    public com.google.protobuf.ByteString
        getLineBytes() {
      java.lang.Object ref = line_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        line_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *产线编号
     * </pre>
     *
     * <code>string line = 3;</code>
     * @param value The line to set.
     * @return This builder for chaining.
     */
    public Builder setLine(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      line_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *产线编号
     * </pre>
     *
     * <code>string line = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearLine() {
      line_ = getDefaultInstance().getLine();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *产线编号
     * </pre>
     *
     * <code>string line = 3;</code>
     * @param value The bytes for line to set.
     * @return This builder for chaining.
     */
    public Builder setLineBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      line_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object orderPeriod_ = "";
    /**
     * <pre>
     *订单周期（年月或周） eg. 2025-01
     * </pre>
     *
     * <code>string order_period = 4;</code>
     * @return The orderPeriod.
     */
    public java.lang.String getOrderPeriod() {
      java.lang.Object ref = orderPeriod_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        orderPeriod_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *订单周期（年月或周） eg. 2025-01
     * </pre>
     *
     * <code>string order_period = 4;</code>
     * @return The bytes for orderPeriod.
     */
    public com.google.protobuf.ByteString
        getOrderPeriodBytes() {
      java.lang.Object ref = orderPeriod_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        orderPeriod_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *订单周期（年月或周） eg. 2025-01
     * </pre>
     *
     * <code>string order_period = 4;</code>
     * @param value The orderPeriod to set.
     * @return This builder for chaining.
     */
    public Builder setOrderPeriod(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      orderPeriod_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *订单周期（年月或周） eg. 2025-01
     * </pre>
     *
     * <code>string order_period = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearOrderPeriod() {
      orderPeriod_ = getDefaultInstance().getOrderPeriod();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *订单周期（年月或周） eg. 2025-01
     * </pre>
     *
     * <code>string order_period = 4;</code>
     * @param value The bytes for orderPeriod to set.
     * @return This builder for chaining.
     */
    public Builder setOrderPeriodBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      orderPeriod_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private java.lang.Object productionPeriod_ = "";
    /**
     * <pre>
     *排产所在周期(年月或周) eg. 2025-01
     * </pre>
     *
     * <code>string production_period = 5;</code>
     * @return The productionPeriod.
     */
    public java.lang.String getProductionPeriod() {
      java.lang.Object ref = productionPeriod_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        productionPeriod_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *排产所在周期(年月或周) eg. 2025-01
     * </pre>
     *
     * <code>string production_period = 5;</code>
     * @return The bytes for productionPeriod.
     */
    public com.google.protobuf.ByteString
        getProductionPeriodBytes() {
      java.lang.Object ref = productionPeriod_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        productionPeriod_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *排产所在周期(年月或周) eg. 2025-01
     * </pre>
     *
     * <code>string production_period = 5;</code>
     * @param value The productionPeriod to set.
     * @return This builder for chaining.
     */
    public Builder setProductionPeriod(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      productionPeriod_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *排产所在周期(年月或周) eg. 2025-01
     * </pre>
     *
     * <code>string production_period = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearProductionPeriod() {
      productionPeriod_ = getDefaultInstance().getProductionPeriod();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *排产所在周期(年月或周) eg. 2025-01
     * </pre>
     *
     * <code>string production_period = 5;</code>
     * @param value The bytes for productionPeriod to set.
     * @return This builder for chaining.
     */
    public Builder setProductionPeriodBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      productionPeriod_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private int quantity_ ;
    /**
     * <pre>
     *生产数量
     * </pre>
     *
     * <code>int32 quantity = 6;</code>
     * @return The quantity.
     */
    @java.lang.Override
    public int getQuantity() {
      return quantity_;
    }
    /**
     * <pre>
     *生产数量
     * </pre>
     *
     * <code>int32 quantity = 6;</code>
     * @param value The quantity to set.
     * @return This builder for chaining.
     */
    public Builder setQuantity(int value) {

      quantity_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *生产数量
     * </pre>
     *
     * <code>int32 quantity = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearQuantity() {
      bitField0_ = (bitField0_ & ~0x00000020);
      quantity_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object costTime_ = "";
    /**
     * <pre>
     *消耗时间
     * </pre>
     *
     * <code>string cost_time = 7;</code>
     * @return The costTime.
     */
    public java.lang.String getCostTime() {
      java.lang.Object ref = costTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        costTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *消耗时间
     * </pre>
     *
     * <code>string cost_time = 7;</code>
     * @return The bytes for costTime.
     */
    public com.google.protobuf.ByteString
        getCostTimeBytes() {
      java.lang.Object ref = costTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        costTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *消耗时间
     * </pre>
     *
     * <code>string cost_time = 7;</code>
     * @param value The costTime to set.
     * @return This builder for chaining.
     */
    public Builder setCostTime(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      costTime_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *消耗时间
     * </pre>
     *
     * <code>string cost_time = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearCostTime() {
      costTime_ = getDefaultInstance().getCostTime();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *消耗时间
     * </pre>
     *
     * <code>string cost_time = 7;</code>
     * @param value The bytes for costTime to set.
     * @return This builder for chaining.
     */
    public Builder setCostTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      costTime_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private java.lang.Object model_ = "";
    /**
     * <pre>
     *平台
     * </pre>
     *
     * <code>string model = 8;</code>
     * @return The model.
     */
    public java.lang.String getModel() {
      java.lang.Object ref = model_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        model_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *平台
     * </pre>
     *
     * <code>string model = 8;</code>
     * @return The bytes for model.
     */
    public com.google.protobuf.ByteString
        getModelBytes() {
      java.lang.Object ref = model_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        model_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *平台
     * </pre>
     *
     * <code>string model = 8;</code>
     * @param value The model to set.
     * @return This builder for chaining.
     */
    public Builder setModel(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      model_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *平台
     * </pre>
     *
     * <code>string model = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearModel() {
      model_ = getDefaultInstance().getModel();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *平台
     * </pre>
     *
     * <code>string model = 8;</code>
     * @param value The bytes for model to set.
     * @return This builder for chaining.
     */
    public Builder setModelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      model_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.hongjingwh.lp.Plan)
  }

  // @@protoc_insertion_point(class_scope:com.hongjingwh.lp.Plan)
  private static final com.github.cret.web.lineLoad.grpc.generate.Plan DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.github.cret.web.lineLoad.grpc.generate.Plan();
  }

  public static com.github.cret.web.lineLoad.grpc.generate.Plan getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Plan>
      PARSER = new com.google.protobuf.AbstractParser<Plan>() {
    @java.lang.Override
    public Plan parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<Plan> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Plan> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.github.cret.web.lineLoad.grpc.generate.Plan getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

