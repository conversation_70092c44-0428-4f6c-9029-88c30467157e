package com.github.cret.web.lineLoad.controller;

import com.github.cret.web.lineLoad.entity.vo.req.CtReq;
import com.github.cret.web.lineLoad.entity.vo.req.LineLoadReq;
import com.github.cret.web.lineLoad.entity.vo.res.CtRes;
import com.github.cret.web.lineLoad.entity.vo.res.LineLoadRes;
import com.github.cret.web.lineLoad.entity.vo.res.OrderRes;
import com.github.cret.web.lineLoad.service.AnalysisService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/analysis")
public class AnalysisController {

    @Resource
    private AnalysisService analysisService;

    @GetMapping("getLoadByLineAndMonth")
    public List<LineLoadRes> getLoadByLineAndMonth(@RequestParam("line") String line,
                                                   @RequestParam("startMonth") String startMonth,
                                                   @RequestParam("endMonth") String endMonth,
                                                   @RequestParam("startUploadDate") String startUploadDate,
                                                   @RequestParam("endUploadDate") String endUploadDate
    ) {
        LineLoadReq lineLoadReq = new LineLoadReq();
        lineLoadReq.setLine(line);
        lineLoadReq.setStartMonth(startMonth);
        lineLoadReq.setEndMonth(endMonth);
        lineLoadReq.setStartUploadDate(startUploadDate);
        lineLoadReq.setEndUploadDate(endUploadDate);
        return analysisService.getLoadByLineAndMonth(lineLoadReq);
    }

    @PostMapping("ct")
    public List<CtRes> analysisCt(@RequestBody CtReq ctReq) {
        return analysisService.analysisCt(ctReq.getLine(), ctReq.getPartNum(), ctReq.getStartCreateTime(), ctReq.getEndCreateTime());
    }

    @GetMapping("order")
    public List<OrderRes> analysisOrder(@RequestParam("partNum") String partNum,
                                        @RequestParam("periodType") String periodType,
                                        @RequestParam("beginOrderDate") String beginOrderDate,
                                        @RequestParam("endOrderDate") String endOrderDate,
                                        @RequestParam("beginUploadDate") String beginUploadDate,
                                        @RequestParam("endUploadDate") String endUploadDate
    ) {
        return analysisService.analysisOrder(partNum, periodType, beginOrderDate, endOrderDate, beginUploadDate, endUploadDate);
    }

}
