// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: IntelligentSchedule.proto

// Protobuf Java Version: 3.25.1
package com.github.cret.web.lineLoad.grpc.generate;

public interface CtOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.hongjingwh.lp.Ct)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *产品物料编码
   * </pre>
   *
   * <code>string part_number = 1;</code>
   * @return The partNumber.
   */
  java.lang.String getPartNumber();
  /**
   * <pre>
   *产品物料编码
   * </pre>
   *
   * <code>string part_number = 1;</code>
   * @return The bytes for partNumber.
   */
  com.google.protobuf.ByteString
      getPartNumberBytes();

  /**
   * <pre>
   *产线编号
   * </pre>
   *
   * <code>string line = 2;</code>
   * @return The line.
   */
  java.lang.String getLine();
  /**
   * <pre>
   *产线编号
   * </pre>
   *
   * <code>string line = 2;</code>
   * @return The bytes for line.
   */
  com.google.protobuf.ByteString
      getLineBytes();

  /**
   * <pre>
   *生产cycle time，单位 秒
   * </pre>
   *
   * <code>string ct = 3;</code>
   * @return The ct.
   */
  java.lang.String getCt();
  /**
   * <pre>
   *生产cycle time，单位 秒
   * </pre>
   *
   * <code>string ct = 3;</code>
   * @return The bytes for ct.
   */
  com.google.protobuf.ByteString
      getCtBytes();
}
