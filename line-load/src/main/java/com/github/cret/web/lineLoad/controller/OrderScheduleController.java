package com.github.cret.web.lineLoad.controller;

import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.lineLoad.document.OrderSchedule;
import com.github.cret.web.lineLoad.service.OrderScheduleService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Description: 订单排班
 * date: 2025/7/9 13:19
 *
 * @author: zxk
 */
@RestController
@RequestMapping("/orderSchedule")
public class OrderScheduleController {

    @Resource
    private OrderScheduleService orderScheduleService;


    /**
     * 获取某日期上传订单某工段某时间周期的排产计划
     */
    @PreAuthorize("hasAuthority('listSchedule')")
    @GetMapping("/{workSection}/{uploadDate}/{timePeriod}")
    public OrderSchedule getSchedule(@PathVariable("workSection") String workSection, @PathVariable(name = "uploadDate") String uploadDate, @PathVariable("timePeriod") String timePeriod) {
        OrderSchedule orderSchedule = orderScheduleService.getSchedule(uploadDate, workSection, timePeriod);
        if (orderSchedule == null){
            throw new RuntimeException("未找到排班计划");
        }
        return orderSchedule;
    }

    /**
     * 保存排班计划
     */
    @PreAuthorize("hasAuthority('saveSchedule')")
    @PostMapping("")
    public void save(@RequestBody OrderSchedule orderSchedule) {
        orderScheduleService.save(orderSchedule);

    }

    @GetMapping("/board")
    public List<OrderSchedule> board() {
        return orderScheduleService.board();
    }

    /**
     * 导出排班计划Excel
     */
//    @PreAuthorize("hasAuthority('exportSchedule')")
    @GetMapping("/exportSchedule")
    public void exportSchedule(@RequestParam("uploadDate") String uploadDate,
                              @RequestParam("workSection") String workSection,
                              HttpServletResponse response) {
        try {
            orderScheduleService.exportSchedule(uploadDate, workSection, response);
        } catch (Exception e) {
            throw new RuntimeException("导出Excel失败: " + e.getMessage(), e);
        }
    }












//    @Resource
//    private WxWorkConfig wxWorkConfig;
//    @GetMapping("test")
//    public void test() {
//        Calendar calendar = Calendar.getInstance();
//        String today = DateUtil.format(calendar.getTime(), "yyyy-MM-dd");
//        calendar.add(Calendar.DAY_OF_MONTH, -1);
//        String datBeforeOne = DateUtil.format(calendar.getTime(), "yyyy-MM-dd");
//        calendar.add(Calendar.DAY_OF_MONTH, -1);
//        String datBeforeTwo = DateUtil.format(calendar.getTime(), "yyyy-MM-dd");
//        calendar.add(Calendar.DAY_OF_MONTH, -1);
//        String datBeforeThree = DateUtil.format(calendar.getTime(), "yyyy-MM-dd");
//        List<String> uploadDates = Arrays.asList(today, datBeforeOne, datBeforeTwo, datBeforeThree);
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");
//        //回到今天
//        calendar.add(Calendar.DAY_OF_MONTH, 3);
//        if (!orderScheduleService.checkOrderExistForDate(uploadDates)) {
//            String toUser = "8594";
//            String content = "<div class=\"gray\">" + dateFormat.format(calendar.getTime())+"</div> <div class=\"highlight\">本周未还上传订单进行测算，请知悉！</div>";
//            TextCard textCard = new TextCard(toUser, null, null, "线体负荷测算系统通知", content, "https://itsm.hongjing-wh.com:18975", wxWorkConfig.getEnableIdTrans(), wxWorkConfig);
//            sendWxWorkMsg(JSONUtil.toJsonStr(textCard));
//            return;
//        }
//        if (!orderScheduleService.checkOrderPlanExistForDate(uploadDates)) {
//            String toUser = "8594";
//            String content = "<div class=\"gray\">" + dateFormat.format(calendar.getTime())+"</div> <div class=\"highlight\">本周上传的订单未完成排产，请知悉！</div>";
//            TextCard textCard = new TextCard(toUser, null, null, "线体负荷测算系统通知", content, "https://itsm.hongjing-wh.com:18975", wxWorkConfig.getEnableIdTrans(), wxWorkConfig);
//            sendWxWorkMsg(JSONUtil.toJsonStr(textCard));
//        }
//    }
//    private void sendWxWorkMsg(String msg) {
//        String apiUrl = wxWorkConfig.getSendTextMsgUrl();
//        String result = HttpUtil.createPost(apiUrl).contentType("application/json")
//                .body(msg)
//                .execute()
//                .body();
//        System.out.println(result);
//    }


}
