package com.github.cret.web.lineLoad.controller;


import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.lineLoad.entity.vo.CtVo;
import com.github.cret.web.lineLoad.service.CtMaintenanceService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;


@RestController
@RequestMapping("/ctMaintenance")
public class CtMaintenanceController {

    private static final Logger log = LoggerFactory.getLogger(CtMaintenanceController.class);

    @Resource
    private CtMaintenanceService ctMaintenanceService;

    /**
     * 上传CT维护Excel文件
     */
    @PreAuthorize("hasAuthority('uploadCt')")
    @PostMapping("/upload")
    public ResponseEntity<String> upload(@RequestParam("file") MultipartFile excel) {
        try {
            if (excel.isEmpty()) {
                return ResponseEntity.badRequest().body("文件不能为空");
            }
            ctMaintenanceService.upload(excel);
            return ResponseEntity.ok("文件上传并解析成功");
        } catch (IOException e) {
            log.error("上传CT维护文件失败", e);
            throw new RuntimeException("上传CT维护文件失败", e);
        } catch (Exception e) {
            log.error("处理CT维护文件时发生未知错误", e);
            throw new RuntimeException("处理文件时发生错误", e);
        }
    }

    /**
     * 获取列表
     */
    @PreAuthorize("hasAuthority('listCt')")
    @GetMapping("/list")
    public List<CtVo> list() {
        return ctMaintenanceService.list();
    }

    /**
     * 获取某日期上传订单某工段的CT信息
     */
    @PreAuthorize("hasAuthority('getCt')")
    @GetMapping("/{workSection}/{uploadDate}")
    public List<CtVo> getOrderCt(@PathVariable("workSection") String workSection, @PathVariable("uploadDate") String uploadDate) {
        return ctMaintenanceService.getOrderCt(uploadDate, workSection);
    }

}

