package com.github.cret.web.lineLoad.document;


import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;
import java.util.List;

/**
 * Description: 周/月度 订单上传
 * date: 2025/7/9 10:19
 *
 * @author: zxk
 */
@Document("order_period_plan")
public class OrderPeriodPlan {

	@Id
	private String id;

	//上传日期
	@Field(name = "upload_date")
	private String uploadDate;

	//文件名称
	@Field(name = "file_name")
	private String fileName;

	//产品号
	@Field(name = "part_number")
	private String partNumber;

	//平台
	@Field(name = "model")
	private String model;

	//产品
	@Field(name = "product")
	private String product;

	//发布日期
	@Field(name = "publish_date")
	private String publishDate;

	//周期订单数
	@Field(name = "period_number")
	private List<PeriodNumber> periodNumberList;

	//周期类型
	@Field(name = "time_period")
	private String timePeriod;

	// 创建时间
	@Field(value = "create_time")
	private Date createTime;

	// 创建人
	@Field(value = "creator")
	private String creator;

	//版本号
	@Field(name = "version")
	private Integer version;

	//文件id
	@Field(name = "file_id")
	private String fileId;

	//文件夹+文件名
	@Field(name = "file_key")
	private String fileKey;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getUploadDate() {
		return uploadDate;
	}

	public void setUploadDate(String uploadDate) {
		this.uploadDate = uploadDate;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getPartNumber() {
		return partNumber;
	}

	public void setPartNumber(String partNumber) {
		this.partNumber = partNumber;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getProduct() {
		return product;
	}

	public void setProduct(String product) {
		this.product = product;
	}

	public List<PeriodNumber> getPeriodNumberList() {
		return periodNumberList;
	}

	public void setPeriodNumberList(List<PeriodNumber> periodNumberList) {
		this.periodNumberList = periodNumberList;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Integer getVersion() {
		return version;
	}

	public void setVersion(Integer version) {
		this.version = version;
	}

	public String getFileId() {
		return fileId;
	}

	public void setFileId(String fileId) {
		this.fileId = fileId;
	}

	public String getPublishDate() {
		return publishDate;
	}

	public void setPublishDate(String publishDate) {
		this.publishDate = publishDate;
	}

	public String getTimePeriod() {
		return timePeriod;
	}

	public void setTimePeriod(String timePeriod) {
		this.timePeriod = timePeriod;
	}

	public String getFileKey() {
		return fileKey;
	}

	public void setFileKey(String fileKey) {
		this.fileKey = fileKey;
	}
}