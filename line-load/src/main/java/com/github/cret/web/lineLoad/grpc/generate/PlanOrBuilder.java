// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: IntelligentSchedule.proto

// Protobuf Java Version: 3.25.1
package com.github.cret.web.lineLoad.grpc.generate;

public interface PlanOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.hongjingwh.lp.Plan)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *产品物料编码
   * </pre>
   *
   * <code>string part_number = 1;</code>
   * @return The partNumber.
   */
  java.lang.String getPartNumber();
  /**
   * <pre>
   *产品物料编码
   * </pre>
   *
   * <code>string part_number = 1;</code>
   * @return The bytes for partNumber.
   */
  com.google.protobuf.ByteString
      getPartNumberBytes();

  /**
   * <pre>
   *产品名称
   * </pre>
   *
   * <code>string product_name = 2;</code>
   * @return The productName.
   */
  java.lang.String getProductName();
  /**
   * <pre>
   *产品名称
   * </pre>
   *
   * <code>string product_name = 2;</code>
   * @return The bytes for productName.
   */
  com.google.protobuf.ByteString
      getProductNameBytes();

  /**
   * <pre>
   *产线编号
   * </pre>
   *
   * <code>string line = 3;</code>
   * @return The line.
   */
  java.lang.String getLine();
  /**
   * <pre>
   *产线编号
   * </pre>
   *
   * <code>string line = 3;</code>
   * @return The bytes for line.
   */
  com.google.protobuf.ByteString
      getLineBytes();

  /**
   * <pre>
   *订单周期（年月或周） eg. 2025-01
   * </pre>
   *
   * <code>string order_period = 4;</code>
   * @return The orderPeriod.
   */
  java.lang.String getOrderPeriod();
  /**
   * <pre>
   *订单周期（年月或周） eg. 2025-01
   * </pre>
   *
   * <code>string order_period = 4;</code>
   * @return The bytes for orderPeriod.
   */
  com.google.protobuf.ByteString
      getOrderPeriodBytes();

  /**
   * <pre>
   *排产所在周期(年月或周) eg. 2025-01
   * </pre>
   *
   * <code>string production_period = 5;</code>
   * @return The productionPeriod.
   */
  java.lang.String getProductionPeriod();
  /**
   * <pre>
   *排产所在周期(年月或周) eg. 2025-01
   * </pre>
   *
   * <code>string production_period = 5;</code>
   * @return The bytes for productionPeriod.
   */
  com.google.protobuf.ByteString
      getProductionPeriodBytes();

  /**
   * <pre>
   *生产数量
   * </pre>
   *
   * <code>int32 quantity = 6;</code>
   * @return The quantity.
   */
  int getQuantity();

  /**
   * <pre>
   *消耗时间
   * </pre>
   *
   * <code>string cost_time = 7;</code>
   * @return The costTime.
   */
  java.lang.String getCostTime();
  /**
   * <pre>
   *消耗时间
   * </pre>
   *
   * <code>string cost_time = 7;</code>
   * @return The bytes for costTime.
   */
  com.google.protobuf.ByteString
      getCostTimeBytes();

  /**
   * <pre>
   *平台
   * </pre>
   *
   * <code>string model = 8;</code>
   * @return The model.
   */
  java.lang.String getModel();
  /**
   * <pre>
   *平台
   * </pre>
   *
   * <code>string model = 8;</code>
   * @return The bytes for model.
   */
  com.google.protobuf.ByteString
      getModelBytes();
}
