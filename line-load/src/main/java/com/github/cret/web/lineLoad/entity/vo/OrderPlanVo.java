package com.github.cret.web.lineLoad.entity.vo;


public class OrderPlanVo {

    //只读模式
    private Boolean readOnly;

    //上传日期
    private String uploadDate;

    //文件名称
    private String fileName;

    private String fileId;

    private String fileKey;

    public String getFileKey() {
        return fileKey;
    }

    public void setFileKey(String fileKey) {
        this.fileKey = fileKey;
    }

    //上传人
    private String creator;

    public String getUploadDate() {
        return uploadDate;
    }

    public void setUploadDate(String uploadDate) {
        this.uploadDate = uploadDate;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public Boolean getReadOnly() {
        return readOnly;
    }

    public void setReadOnly(Boolean readOnly) {
        this.readOnly = readOnly;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public OrderPlanVo(String uploadDate, String fileName, String fileId, Boolean readOnly, String creator, String fileKey) {
        this.uploadDate = uploadDate;
        this.fileId = fileId;
        this.fileName = fileName;
        this.readOnly = readOnly;
        this.creator = creator;
        this.fileKey = fileKey;
    }
}
