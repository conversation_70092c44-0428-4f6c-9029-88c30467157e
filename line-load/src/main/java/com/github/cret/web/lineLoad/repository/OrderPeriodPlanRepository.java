package com.github.cret.web.lineLoad.repository;


import com.github.cret.web.lineLoad.document.OrderPeriodPlan;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface OrderPeriodPlanRepository extends MongoRepository<OrderPeriodPlan, String> {

    @Aggregation({
            "{ $group: { _id: '$part_number' } }",
    })
    List<String> findDistinctPartNumber();

    int countByUploadDate(String uploadDate);

    List<OrderPeriodPlan> findByUploadDate(String uploadDate);


    @Aggregation({"{ $match: { upload_date: ?0 } }", "{ $group: { _id: null, maxVersion: { $max: '$version' } }}"})
    Optional<Integer> findMaxVersionByUploadDate(String uploadDate);

    //按uploadDate找到最大版本的数据
    @Aggregation({
            "{ $match: { upload_date: ?0} }",
            "{ $sort: { version: -1 } }",
            "{ $group: { _id: null, maxVersion: { $first: '$version' }, docs: { $push: '$$ROOT' } }}",
            "{ $project: {items: {$filter: { input: '$docs',as: 'doc', cond: { $eq: [ '$$doc.version', '$maxVersion' ] }}}}}",
            "{ $unwind: '$items' }",
            "{ $replaceRoot: { newRoot: '$items' }}",
    })
    List<OrderPeriodPlan> findLatestVersionByUploadDate(String uploadDate);

    //按uploadDate和version倒序排列，找到第一条数据
    @Aggregation({
            "{ $sort: { upload_date: -1, version: -1 } }",
            "{ $limit: 1 } ",
    })
    OrderPeriodPlan findLatestActiveFileId();

    //判断多个日期下的订单计划数据是否存在
    @Aggregation({"{ $match: { upload_date: { $in: ?0 } } }", "{ $group: { _id: null, count: { $sum: 1 } }}"})
    Integer countForUploadDate(List<String> uploadDates);

}

