package com.github.cret.web.lineLoad.document;

import org.springframework.data.mongodb.core.mapping.Field;

/**
 * Description: 周期内数量 月或周
 * date: 2025/7/9 10:20
 *
 * @author: zxk
 */
public class PeriodNumber {

    //周期
    @Field(name = "period")
    private String period;

    //数量
    @Field(name = "number")
    private Integer number;

    public PeriodNumber() {
    }

    public PeriodNumber(String period, Integer number) {
        this.period = period;
        this.number = number;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }
}
