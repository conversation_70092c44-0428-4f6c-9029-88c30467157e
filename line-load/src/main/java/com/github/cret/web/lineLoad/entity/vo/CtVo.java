package com.github.cret.web.lineLoad.entity.vo;

public class CtVo {

    //产品物料编码
    private String partNumber;
    //产线编号
    private String line;
    //工段
    private String workSection;
    //生产cycle time，单位 秒
    private String ct;
    //版面
    private String board;

    private String model;

    public String getPartNumber() {
        return partNumber;
    }

    public void setPartNumber(String partNumber) {
        this.partNumber = partNumber;
    }

    public String getCt() {
        return ct;
    }

    public void setCt(String ct) {
        this.ct = ct;
    }

    public String getLine() {
        return line;
    }

    public void setLine(String line) {
        this.line = line;
    }

    public String getWorkSection() {
        return workSection;
    }

    public void setWorkSection(String workSection) {
        this.workSection = workSection;
    }

    public String getBoard() {
        return board;
    }

    public void setBoard(String board) {
        this.board = board;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public CtVo(String partNumber, String line, String workSection, String ct, String board, String model) {
        this.partNumber = partNumber;
        this.line = line;
        this.workSection = workSection;
        this.ct = ct;
        this.board = board;
        this.model = model;
    }
}
