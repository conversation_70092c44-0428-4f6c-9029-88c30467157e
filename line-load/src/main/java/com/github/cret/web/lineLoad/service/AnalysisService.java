package com.github.cret.web.lineLoad.service;

import com.github.cret.web.lineLoad.entity.vo.req.LineLoadReq;
import com.github.cret.web.lineLoad.entity.vo.res.CtRes;
import com.github.cret.web.lineLoad.entity.vo.res.LineLoadRes;
import com.github.cret.web.lineLoad.entity.vo.res.OrderRes;

import java.util.Date;
import java.util.List;

public interface AnalysisService {

    List<LineLoadRes> getLoadByLineAndMonth(LineLoadReq lineLoadReq);

    List<CtRes> analysisCt(String line, String partNum, Date startCreateTime, Date endCreateTime);

    List<OrderRes> analysisOrder(String partNum, String periodType, String beginOrderDate, String endOrderDate, String beginUploadDate, String endUploadDate);
}
