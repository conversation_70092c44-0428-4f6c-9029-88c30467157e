package com.github.cret.web.lineLoad.document;


import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;
import java.util.List;

/**
 * Description: 临时 线体-产品号-工序维护，用于解决部分样机或未生产的产品号无真实ct的问题
 * date: 2025/7/9 10:19
 *
 * @author: zxk
 */
@Document("temp_ct_maintenance")
public class TempCtMaintenance {

	@Id
	private String id;

	//线体
	@Field(name = "line")
	private String line;

	//产品号
	@Field(name = "part_number")
	private String partNumber;

	//平台
	@Field(name = "model")
	private String model;

	//工段：SMT DIP
	@Field(name = "work_section")
	private String workSection;

	//ct
	@Field(name = "ct")
	private String ct;

	// 创建时间
	@Field(value = "create_time")
	private Date createTime;

	// 创建人
	@Field(value = "creator")
	private String creator;

	//更新时间
	@Field(value = "update_time")
	private Date updateTime;

	//更新人
	@Field(value = "updater")
	private String updater;


	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getLine() {
		return line;
	}

	public void setLine(String line) {
		this.line = line;
	}

	public String getPartNumber() {
		return partNumber;
	}

	public void setPartNumber(String partNumber) {
		this.partNumber = partNumber;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getWorkSection() {
		return workSection;
	}

	public void setWorkSection(String workSection) {
		this.workSection = workSection;
	}

	public String getCt() {
		return ct;
	}

	public void setCt(String ct) {
		this.ct = ct;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getUpdater() {
		return updater;
	}

	public void setUpdater(String updater) {
		this.updater = updater;
	}
}