package com.github.cret.web.lineLoad.document;

import org.springframework.data.mongodb.core.mapping.Field;

public class LinePeriodWorkDay {


    //线体
    @Field(name = "line")
    private String line;

    //周期的工作日天数
    @Field(name = "period_work_days")
    private String periodWorkDays;

    //周期
    @Field(name = "")
    private String period;

    public LinePeriodWorkDay(String line, String periodWorkDays, String period) {
        this.line = line;
        this.periodWorkDays = periodWorkDays;
        this.period = period;
    }

    public String getLine() {
        return line;
    }

    public void setLine(String line) {
        this.line = line;
    }

    public String getPeriodWorkDays() {
        return periodWorkDays;
    }

    public void setPeriodWorkDays(String periodWorkDays) {
        this.periodWorkDays = periodWorkDays;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

}
