package com.github.cret.web.lineLoad.entity.wxwork;


import com.github.cret.web.lineLoad.config.WxWorkConfig;

public class BaseReq {

    private String touser;
    private String toparty;
    private String totag;
    private String msgtype;
    private int agentid;
    Integer enable_duplicate_check;
    Integer duplicate_check_interval;

    public String getTouser() {
        return touser;
    }

    public void setTouser(String touser) {
        this.touser = touser;
    }

    public String getToparty() {
        return toparty;
    }

    public void setToparty(String toparty) {
        this.toparty = toparty;
    }

    public String getTotag() {
        return totag;
    }

    public void setTotag(String totag) {
        this.totag = totag;
    }

    public String getMsgtype() {
        return msgtype;
    }

    public void setMsgtype(String msgtype) {
        this.msgtype = msgtype;
    }

    public int getAgentid() {
        return agentid;
    }

    public void setAgentid(int agentid) {
        this.agentid = agentid;
    }

    public Integer getEnable_duplicate_check() {
        return enable_duplicate_check;
    }

    public void setEnable_duplicate_check(Integer enable_duplicate_check) {
        this.enable_duplicate_check = enable_duplicate_check;
    }

    public Integer getDuplicate_check_interval() {
        return duplicate_check_interval;
    }

    public void setDuplicate_check_interval(Integer duplicate_check_interval) {
        this.duplicate_check_interval = duplicate_check_interval;
    }

    BaseReq() {

    }

    BaseReq(String toUser, String toParty, String toTag, String msgType, WxWorkConfig wxWorkConfig) {
        this.touser = toUser;
        this.toparty = toParty;
        this.totag = toTag;
        this.msgtype = msgType;
        this.agentid = wxWorkConfig.getAgentId();
        this.enable_duplicate_check = wxWorkConfig.getEnableDuplicateCheck();
        this.duplicate_check_interval = wxWorkConfig.getDuplicateCheckInterval();
    }

}
