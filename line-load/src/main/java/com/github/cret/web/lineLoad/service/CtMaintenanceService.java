package com.github.cret.web.lineLoad.service;


import com.github.cret.web.lineLoad.entity.vo.CtVo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;


public interface CtMaintenanceService {

    void upload(MultipartFile excel) throws IOException;

    List<CtVo> getOrderCt(String uploadDate, String workSection);

    List<CtVo> listAllCt();

    List<CtVo> list();

}

