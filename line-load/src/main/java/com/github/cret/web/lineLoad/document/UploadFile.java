package com.github.cret.web.lineLoad.document;


import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

@Document("upload_file")
public class UploadFile {

	@Id
	private String id;

	//文件类型 1:订单文件 2:CT维护文件 3:线体班数维护文件
	@Field(name = "file_type")
	private Integer fileType;

	//文件名
	@Field(name = "file_name")
	private String fileName;

	//文件key
	@Field(name = "file_key")
	private String fileKey;


	// 创建时间
	@Field(value = "create_time")
	private Date createTime;

	// 创建人
	@Field(value = "creator")
	private String creator;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Integer getFileType() {
		return fileType;
	}

	public void setFileType(Integer fileType) {
		this.fileType = fileType;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getFileKey() {
		return fileKey;
	}

	public void setFileKey(String fileKey) {
		this.fileKey = fileKey;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public UploadFile(Integer fileType, String fileName, String fileKey, Date createTime, String creator) {
		this.fileType = fileType;
		this.fileName = fileName;
		this.fileKey = fileKey;
		this.createTime = createTime;
		this.creator = creator;
	}
}