package com.github.cret.web.lineLoad.controller;


import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.lineLoad.document.TempCtMaintenance;
import com.github.cret.web.lineLoad.service.TempCtMaintenanceService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/tempCtMaintenance")
public class TempCtMaintenanceController {

    @Resource
    private TempCtMaintenanceService tempCtMaintenanceService;

    /**
     * 获取列表
     */
    @PreAuthorize("hasAuthority('listTempCt')")
    @GetMapping("/list")
    public List<TempCtMaintenance> list() {
        return tempCtMaintenanceService.list();
    }

    /**
     * 获取
     */
    @PreAuthorize("hasAuthority('getTempCt')")
    @GetMapping("/{id}")
    public TempCtMaintenance get(@PathVariable("id") String id) {
        return tempCtMaintenanceService.findById(id);
    }

    /**
     * 保存
     */
    @PreAuthorize("hasAuthority('saveTempCt')")
    @PostMapping("/create")
    public void add(@RequestBody TempCtMaintenance tempCtMaintenance) {
        if (tempCtMaintenanceService.checkExist(tempCtMaintenance)) {
            String errorMsg = String.format("%s 已在 %s 线体上维护CT数据", tempCtMaintenance.getPartNumber(), tempCtMaintenance.getLine());
            throw SysErrEnum.CONFLICT_KEY.exception(errorMsg);
        }
        tempCtMaintenanceService.save(tempCtMaintenance);
    }

    /**
     * 保存
     */
    @PreAuthorize("hasAuthority('saveTempCt')")
    @PostMapping("/update/{id}")
    public void update(@PathVariable("id") String id, @RequestBody TempCtMaintenance tempCtMaintenance) {
        tempCtMaintenance.setId(id);
        if (tempCtMaintenanceService.checkExist(tempCtMaintenance)) {
            String errorMsg = String.format("%s 已在 %s 线体上维护CT数据", tempCtMaintenance.getPartNumber(), tempCtMaintenance.getLine());
            throw SysErrEnum.CONFLICT_KEY.exception(errorMsg);
        }
        tempCtMaintenanceService.save(tempCtMaintenance);
    }


    /**
     * 删除
     */
    @PreAuthorize("hasAuthority('deleteTempCt')")
    @PostMapping("/delete/{id}")
    public void delete(@PathVariable("id") String id) {
        tempCtMaintenanceService.deleteById(id);
    }

}

