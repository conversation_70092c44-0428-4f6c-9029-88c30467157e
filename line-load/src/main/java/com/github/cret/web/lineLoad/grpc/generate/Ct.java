// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: IntelligentSchedule.proto

// Protobuf Java Version: 3.25.1
package com.github.cret.web.lineLoad.grpc.generate;

/**
 * Protobuf type {@code com.hongjingwh.lp.Ct}
 */
public final class Ct extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.hongjingwh.lp.Ct)
    CtOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Ct.newBuilder() to construct.
  private Ct(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Ct() {
    partNumber_ = "";
    line_ = "";
    ct_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Ct();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.internal_static_com_hongjingwh_lp_Ct_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.internal_static_com_hongjingwh_lp_Ct_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.github.cret.web.lineLoad.grpc.generate.Ct.class, com.github.cret.web.lineLoad.grpc.generate.Ct.Builder.class);
  }

  public static final int PART_NUMBER_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object partNumber_ = "";
  /**
   * <pre>
   *产品物料编码
   * </pre>
   *
   * <code>string part_number = 1;</code>
   * @return The partNumber.
   */
  @java.lang.Override
  public java.lang.String getPartNumber() {
    java.lang.Object ref = partNumber_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      partNumber_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *产品物料编码
   * </pre>
   *
   * <code>string part_number = 1;</code>
   * @return The bytes for partNumber.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPartNumberBytes() {
    java.lang.Object ref = partNumber_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      partNumber_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LINE_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object line_ = "";
  /**
   * <pre>
   *产线编号
   * </pre>
   *
   * <code>string line = 2;</code>
   * @return The line.
   */
  @java.lang.Override
  public java.lang.String getLine() {
    java.lang.Object ref = line_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      line_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *产线编号
   * </pre>
   *
   * <code>string line = 2;</code>
   * @return The bytes for line.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLineBytes() {
    java.lang.Object ref = line_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      line_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CT_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object ct_ = "";
  /**
   * <pre>
   *生产cycle time，单位 秒
   * </pre>
   *
   * <code>string ct = 3;</code>
   * @return The ct.
   */
  @java.lang.Override
  public java.lang.String getCt() {
    java.lang.Object ref = ct_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      ct_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *生产cycle time，单位 秒
   * </pre>
   *
   * <code>string ct = 3;</code>
   * @return The bytes for ct.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCtBytes() {
    java.lang.Object ref = ct_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      ct_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(partNumber_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, partNumber_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(line_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, line_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(ct_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, ct_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(partNumber_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, partNumber_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(line_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, line_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(ct_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, ct_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.github.cret.web.lineLoad.grpc.generate.Ct)) {
      return super.equals(obj);
    }
    com.github.cret.web.lineLoad.grpc.generate.Ct other = (com.github.cret.web.lineLoad.grpc.generate.Ct) obj;

    if (!getPartNumber()
        .equals(other.getPartNumber())) return false;
    if (!getLine()
        .equals(other.getLine())) return false;
    if (!getCt()
        .equals(other.getCt())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + PART_NUMBER_FIELD_NUMBER;
    hash = (53 * hash) + getPartNumber().hashCode();
    hash = (37 * hash) + LINE_FIELD_NUMBER;
    hash = (53 * hash) + getLine().hashCode();
    hash = (37 * hash) + CT_FIELD_NUMBER;
    hash = (53 * hash) + getCt().hashCode();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.github.cret.web.lineLoad.grpc.generate.Ct parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.Ct parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.Ct parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.Ct parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.Ct parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.Ct parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.Ct parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.Ct parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.github.cret.web.lineLoad.grpc.generate.Ct parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.github.cret.web.lineLoad.grpc.generate.Ct parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.Ct parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.Ct parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.github.cret.web.lineLoad.grpc.generate.Ct prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.hongjingwh.lp.Ct}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.hongjingwh.lp.Ct)
      com.github.cret.web.lineLoad.grpc.generate.CtOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.internal_static_com_hongjingwh_lp_Ct_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.internal_static_com_hongjingwh_lp_Ct_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.github.cret.web.lineLoad.grpc.generate.Ct.class, com.github.cret.web.lineLoad.grpc.generate.Ct.Builder.class);
    }

    // Construct using com.github.cret.web.lineLoad.grpc.generate.Ct.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      partNumber_ = "";
      line_ = "";
      ct_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.internal_static_com_hongjingwh_lp_Ct_descriptor;
    }

    @java.lang.Override
    public com.github.cret.web.lineLoad.grpc.generate.Ct getDefaultInstanceForType() {
      return com.github.cret.web.lineLoad.grpc.generate.Ct.getDefaultInstance();
    }

    @java.lang.Override
    public com.github.cret.web.lineLoad.grpc.generate.Ct build() {
      com.github.cret.web.lineLoad.grpc.generate.Ct result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.github.cret.web.lineLoad.grpc.generate.Ct buildPartial() {
      com.github.cret.web.lineLoad.grpc.generate.Ct result = new com.github.cret.web.lineLoad.grpc.generate.Ct(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.github.cret.web.lineLoad.grpc.generate.Ct result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.partNumber_ = partNumber_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.line_ = line_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.ct_ = ct_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.github.cret.web.lineLoad.grpc.generate.Ct) {
        return mergeFrom((com.github.cret.web.lineLoad.grpc.generate.Ct)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.github.cret.web.lineLoad.grpc.generate.Ct other) {
      if (other == com.github.cret.web.lineLoad.grpc.generate.Ct.getDefaultInstance()) return this;
      if (!other.getPartNumber().isEmpty()) {
        partNumber_ = other.partNumber_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getLine().isEmpty()) {
        line_ = other.line_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (!other.getCt().isEmpty()) {
        ct_ = other.ct_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              partNumber_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              line_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              ct_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object partNumber_ = "";
    /**
     * <pre>
     *产品物料编码
     * </pre>
     *
     * <code>string part_number = 1;</code>
     * @return The partNumber.
     */
    public java.lang.String getPartNumber() {
      java.lang.Object ref = partNumber_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        partNumber_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *产品物料编码
     * </pre>
     *
     * <code>string part_number = 1;</code>
     * @return The bytes for partNumber.
     */
    public com.google.protobuf.ByteString
        getPartNumberBytes() {
      java.lang.Object ref = partNumber_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        partNumber_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *产品物料编码
     * </pre>
     *
     * <code>string part_number = 1;</code>
     * @param value The partNumber to set.
     * @return This builder for chaining.
     */
    public Builder setPartNumber(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      partNumber_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *产品物料编码
     * </pre>
     *
     * <code>string part_number = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearPartNumber() {
      partNumber_ = getDefaultInstance().getPartNumber();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *产品物料编码
     * </pre>
     *
     * <code>string part_number = 1;</code>
     * @param value The bytes for partNumber to set.
     * @return This builder for chaining.
     */
    public Builder setPartNumberBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      partNumber_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object line_ = "";
    /**
     * <pre>
     *产线编号
     * </pre>
     *
     * <code>string line = 2;</code>
     * @return The line.
     */
    public java.lang.String getLine() {
      java.lang.Object ref = line_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        line_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *产线编号
     * </pre>
     *
     * <code>string line = 2;</code>
     * @return The bytes for line.
     */
    public com.google.protobuf.ByteString
        getLineBytes() {
      java.lang.Object ref = line_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        line_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *产线编号
     * </pre>
     *
     * <code>string line = 2;</code>
     * @param value The line to set.
     * @return This builder for chaining.
     */
    public Builder setLine(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      line_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *产线编号
     * </pre>
     *
     * <code>string line = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearLine() {
      line_ = getDefaultInstance().getLine();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *产线编号
     * </pre>
     *
     * <code>string line = 2;</code>
     * @param value The bytes for line to set.
     * @return This builder for chaining.
     */
    public Builder setLineBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      line_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.lang.Object ct_ = "";
    /**
     * <pre>
     *生产cycle time，单位 秒
     * </pre>
     *
     * <code>string ct = 3;</code>
     * @return The ct.
     */
    public java.lang.String getCt() {
      java.lang.Object ref = ct_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        ct_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *生产cycle time，单位 秒
     * </pre>
     *
     * <code>string ct = 3;</code>
     * @return The bytes for ct.
     */
    public com.google.protobuf.ByteString
        getCtBytes() {
      java.lang.Object ref = ct_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ct_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *生产cycle time，单位 秒
     * </pre>
     *
     * <code>string ct = 3;</code>
     * @param value The ct to set.
     * @return This builder for chaining.
     */
    public Builder setCt(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ct_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *生产cycle time，单位 秒
     * </pre>
     *
     * <code>string ct = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearCt() {
      ct_ = getDefaultInstance().getCt();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *生产cycle time，单位 秒
     * </pre>
     *
     * <code>string ct = 3;</code>
     * @param value The bytes for ct to set.
     * @return This builder for chaining.
     */
    public Builder setCtBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      ct_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.hongjingwh.lp.Ct)
  }

  // @@protoc_insertion_point(class_scope:com.hongjingwh.lp.Ct)
  private static final com.github.cret.web.lineLoad.grpc.generate.Ct DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.github.cret.web.lineLoad.grpc.generate.Ct();
  }

  public static com.github.cret.web.lineLoad.grpc.generate.Ct getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Ct>
      PARSER = new com.google.protobuf.AbstractParser<Ct>() {
    @java.lang.Override
    public Ct parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<Ct> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Ct> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.github.cret.web.lineLoad.grpc.generate.Ct getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

