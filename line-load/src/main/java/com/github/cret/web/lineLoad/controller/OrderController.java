package com.github.cret.web.lineLoad.controller;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.lineLoad.entity.vo.OrderPlanVo;
import com.github.cret.web.lineLoad.service.OrderScheduleService;

import jakarta.annotation.Resource;

@RestController
@RequestMapping("/order")
public class OrderController {

    @Resource
    private OrderScheduleService orderScheduleService;

    /**
     * 查询今天是否已经上传订单
     */
    @GetMapping("/checkExist")
    @PreAuthorize("hasAuthority('checkExist')")
    public Boolean checkExist() {
        return orderScheduleService.checkOrderExistToday();
    }

    /**
     * 上传订单文件
     */
    @PreAuthorize("hasAuthority('uploadOrder')")
    @PostMapping("/upload")
    public void upload(@RequestParam("file") MultipartFile excel) {
        orderScheduleService.upload(excel);
    }

    /**
     * 获取列表
     */
    @PreAuthorize("hasAuthority('listOrder')")
    @PostMapping("/page")
    public PageList<OrderPlanVo> page(@RequestBody PageableParam<Object> pageableParam) {
        return orderScheduleService.page(pageableParam);
    }

}
