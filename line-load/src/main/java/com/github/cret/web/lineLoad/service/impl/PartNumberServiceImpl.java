package com.github.cret.web.lineLoad.service.impl;

import com.github.cret.web.lineLoad.repository.OrderPeriodPlanRepository;
import com.github.cret.web.lineLoad.service.PartNumberService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PartNumberServiceImpl implements PartNumberService {

    @Resource
    private OrderPeriodPlanRepository orderPeriodPlanRepository;

    @Override
    public List<String> list() {
        return orderPeriodPlanRepository.findDistinctPartNumber();
    }


}
