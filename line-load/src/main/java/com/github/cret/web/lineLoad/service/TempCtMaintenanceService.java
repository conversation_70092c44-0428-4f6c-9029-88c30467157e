package com.github.cret.web.lineLoad.service;


import com.github.cret.web.lineLoad.document.TempCtMaintenance;

import java.util.List;


public interface TempCtMaintenanceService {

    void save(TempCtMaintenance tempCtMaintenance);

    void deleteById(String id);

    TempCtMaintenance findById(String id);

    List<TempCtMaintenance> list();

    Boolean checkExist(TempCtMaintenance tempCtMaintenance);

}

