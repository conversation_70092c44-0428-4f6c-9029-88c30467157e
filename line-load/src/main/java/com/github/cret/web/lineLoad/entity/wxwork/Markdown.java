package com.github.cret.web.lineLoad.entity.wxwork;

import com.github.cret.web.lineLoad.config.WxWorkConfig;

public class Markdown extends BaseReq {

    private MarkdownContent markdown;
    private int enable_id_trans;

    public int getEnable_id_trans() {
        return enable_id_trans;
    }

    public void setEnable_id_trans(int enable_id_trans) {
        this.enable_id_trans = enable_id_trans;
    }

    public MarkdownContent getMarkdown() {
        return markdown;
    }

    public void setMarkdown(MarkdownContent markdown) {
        this.markdown = markdown;
    }

    public static class MarkdownContent {
        private String content;

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }

    public Markdown(String toUser, String toParty, String toTag, String content, int enable_id_trans, WxWorkConfig wxWorkConfig) {
        super(toUser, toParty, toTag, "markdown", wxWorkConfig);
        this.markdown = new MarkdownContent();
        this.markdown.content = content;
        this.enable_id_trans = enable_id_trans;
    }
}

