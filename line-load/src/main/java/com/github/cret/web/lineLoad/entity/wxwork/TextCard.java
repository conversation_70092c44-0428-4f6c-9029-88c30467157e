package com.github.cret.web.lineLoad.entity.wxwork;

import com.github.cret.web.lineLoad.config.WxWorkConfig;

public class TextCard extends BaseReq{

    private TextCardContent textcard;
    private int enable_id_trans;

    public TextCardContent getTextcard() {
        return textcard;
    }

    public void setTextcard(TextCardContent textcard) {
        this.textcard = textcard;
    }

    public int getEnable_id_trans() {
        return enable_id_trans;
    }

    public void setEnable_id_trans(int enable_id_trans) {
        this.enable_id_trans = enable_id_trans;
    }

    public static class TextCardContent {
        private String title;
        private String description;
        private String url;
        private String btntxt;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getBtntxt() {
            return btntxt;
        }

        public void setBtntxt(String btntxt) {
            this.btntxt = btntxt;
        }

    }

    public TextCard(String toUser, String toParty, String toTag, String title, String content, String url, int enable_id_trans, WxWorkConfig wxWorkConfig) {
        super(toUser, toParty, toTag, "textcard", wxWorkConfig);
        this.textcard = new TextCard.TextCardContent();
        this.textcard.description = content;
        this.textcard.title = title;
        this.textcard.url = url;
        this.textcard.btntxt = "进入系统";
        this.enable_id_trans = enable_id_trans;
    }

}

