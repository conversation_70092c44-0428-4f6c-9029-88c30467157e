package com.github.cret.web.lineLoad.utils;

import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.lineLoad.enums.TimePeriod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.Date;
import java.util.stream.IntStream;

public class TimeUtils {

    private static final Logger log = LoggerFactory.getLogger(TimeUtils.class.getName());

    //把yyyy-MM-dd 或者 yyyy/MM/dd 格式的日期字符串转换成 yyyyMMdd格式
    public static String parsePeriod(String period) {
        try {
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date date = inputFormat.parse(period);
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyyMMdd");
            return outputFormat.format(date);
        } catch (Exception e) {
            try {
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy/MM/dd");
                Date date = inputFormat.parse(period);
                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyyMMdd");
                return outputFormat.format(date);
            } catch (Exception ex) {
                log.error("日期格式化错误，存在错误的表头日期格式： {}", period);
                throw new RuntimeException("存在错误的表头日期格式：" + period);
            }
        }
    }

    //根据月或者周，根据yyyy-MM-dd 或者 yyyy/MM/dd 或yyyyMMdd 格式的日期获得该月或者周的工作日天数
    //如果是月，则返回该月去掉周日后的天数，如果是周，则固定返回6
    public static String getWorkDays(String period, TimePeriod timePeriod) {
        if (timePeriod == TimePeriod.MONTHLY) {
            try {
                period = period.replaceAll("/", "-");
                if (period.contains("-")) {
                    period = period.substring(0, 7);
                } else {
                    period = period.substring(0, 4) + "-" + period.substring(4, 6);
                }
                YearMonth yearMonth = YearMonth.parse(period);
                int totalDays = yearMonth.lengthOfMonth();
                LocalDate startDate = yearMonth.atDay(1);
                long workDays = IntStream.range(0, totalDays)
                        .mapToObj(startDate::plusDays)
                        .filter(date -> !date.getDayOfWeek().equals(DayOfWeek.SUNDAY))
                        .count();
                return String.valueOf(workDays);
            } catch (Exception e) {
                log.error("获取月度工作日天数错误，存在错误的表头日期格式： {}", period);
                throw new RuntimeException("存在错误的表头日期格式：" + period);
            }
        } else {
            return "6";
        }
    }

}
