package com.github.cret.web.lineLoad.controller;

import com.github.cret.web.lineLoad.entity.vo.CtVo;
import com.github.cret.web.lineLoad.entity.vo.res.LineLoadRes;
import com.github.cret.web.lineLoad.service.CtMaintenanceService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/line")
public class LineController {

    @Resource
    private CtMaintenanceService ctMaintenanceService;

    @RequestMapping("/list")
    public List<String> list() {
        return ctMaintenanceService.listAllCt().stream().map(CtVo::getLine).distinct().toList();
    }

}
