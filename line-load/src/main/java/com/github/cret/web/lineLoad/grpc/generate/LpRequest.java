// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: IntelligentSchedule.proto

// Protobuf Java Version: 3.25.1
package com.github.cret.web.lineLoad.grpc.generate;

/**
 * Protobuf type {@code com.hongjingwh.lp.LpRequest}
 */
public final class LpRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.hongjingwh.lp.LpRequest)
    LpRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use LpRequest.newBuilder() to construct.
  private LpRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private LpRequest() {
    orders_ = java.util.Collections.emptyList();
    cts_ = java.util.Collections.emptyList();
    productionTimes_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new LpRequest();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.internal_static_com_hongjingwh_lp_LpRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.internal_static_com_hongjingwh_lp_LpRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.github.cret.web.lineLoad.grpc.generate.LpRequest.class, com.github.cret.web.lineLoad.grpc.generate.LpRequest.Builder.class);
  }

  public static final int ORDERS_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<com.github.cret.web.lineLoad.grpc.generate.Order> orders_;
  /**
   * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
   */
  @java.lang.Override
  public java.util.List<com.github.cret.web.lineLoad.grpc.generate.Order> getOrdersList() {
    return orders_;
  }
  /**
   * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.github.cret.web.lineLoad.grpc.generate.OrderOrBuilder> 
      getOrdersOrBuilderList() {
    return orders_;
  }
  /**
   * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
   */
  @java.lang.Override
  public int getOrdersCount() {
    return orders_.size();
  }
  /**
   * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
   */
  @java.lang.Override
  public com.github.cret.web.lineLoad.grpc.generate.Order getOrders(int index) {
    return orders_.get(index);
  }
  /**
   * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
   */
  @java.lang.Override
  public com.github.cret.web.lineLoad.grpc.generate.OrderOrBuilder getOrdersOrBuilder(
      int index) {
    return orders_.get(index);
  }

  public static final int CTS_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<com.github.cret.web.lineLoad.grpc.generate.Ct> cts_;
  /**
   * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
   */
  @java.lang.Override
  public java.util.List<com.github.cret.web.lineLoad.grpc.generate.Ct> getCtsList() {
    return cts_;
  }
  /**
   * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.github.cret.web.lineLoad.grpc.generate.CtOrBuilder> 
      getCtsOrBuilderList() {
    return cts_;
  }
  /**
   * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
   */
  @java.lang.Override
  public int getCtsCount() {
    return cts_.size();
  }
  /**
   * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
   */
  @java.lang.Override
  public com.github.cret.web.lineLoad.grpc.generate.Ct getCts(int index) {
    return cts_.get(index);
  }
  /**
   * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
   */
  @java.lang.Override
  public com.github.cret.web.lineLoad.grpc.generate.CtOrBuilder getCtsOrBuilder(
      int index) {
    return cts_.get(index);
  }

  public static final int PRODUCTION_TIMES_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<com.github.cret.web.lineLoad.grpc.generate.ProductionTime> productionTimes_;
  /**
   * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
   */
  @java.lang.Override
  public java.util.List<com.github.cret.web.lineLoad.grpc.generate.ProductionTime> getProductionTimesList() {
    return productionTimes_;
  }
  /**
   * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.github.cret.web.lineLoad.grpc.generate.ProductionTimeOrBuilder> 
      getProductionTimesOrBuilderList() {
    return productionTimes_;
  }
  /**
   * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
   */
  @java.lang.Override
  public int getProductionTimesCount() {
    return productionTimes_.size();
  }
  /**
   * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
   */
  @java.lang.Override
  public com.github.cret.web.lineLoad.grpc.generate.ProductionTime getProductionTimes(int index) {
    return productionTimes_.get(index);
  }
  /**
   * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
   */
  @java.lang.Override
  public com.github.cret.web.lineLoad.grpc.generate.ProductionTimeOrBuilder getProductionTimesOrBuilder(
      int index) {
    return productionTimes_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < orders_.size(); i++) {
      output.writeMessage(1, orders_.get(i));
    }
    for (int i = 0; i < cts_.size(); i++) {
      output.writeMessage(2, cts_.get(i));
    }
    for (int i = 0; i < productionTimes_.size(); i++) {
      output.writeMessage(3, productionTimes_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < orders_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, orders_.get(i));
    }
    for (int i = 0; i < cts_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, cts_.get(i));
    }
    for (int i = 0; i < productionTimes_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, productionTimes_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.github.cret.web.lineLoad.grpc.generate.LpRequest)) {
      return super.equals(obj);
    }
    com.github.cret.web.lineLoad.grpc.generate.LpRequest other = (com.github.cret.web.lineLoad.grpc.generate.LpRequest) obj;

    if (!getOrdersList()
        .equals(other.getOrdersList())) return false;
    if (!getCtsList()
        .equals(other.getCtsList())) return false;
    if (!getProductionTimesList()
        .equals(other.getProductionTimesList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getOrdersCount() > 0) {
      hash = (37 * hash) + ORDERS_FIELD_NUMBER;
      hash = (53 * hash) + getOrdersList().hashCode();
    }
    if (getCtsCount() > 0) {
      hash = (37 * hash) + CTS_FIELD_NUMBER;
      hash = (53 * hash) + getCtsList().hashCode();
    }
    if (getProductionTimesCount() > 0) {
      hash = (37 * hash) + PRODUCTION_TIMES_FIELD_NUMBER;
      hash = (53 * hash) + getProductionTimesList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.github.cret.web.lineLoad.grpc.generate.LpRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.LpRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.LpRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.LpRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.LpRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.LpRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.LpRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.LpRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.github.cret.web.lineLoad.grpc.generate.LpRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.github.cret.web.lineLoad.grpc.generate.LpRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.LpRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.LpRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.github.cret.web.lineLoad.grpc.generate.LpRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.hongjingwh.lp.LpRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.hongjingwh.lp.LpRequest)
      com.github.cret.web.lineLoad.grpc.generate.LpRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.internal_static_com_hongjingwh_lp_LpRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.internal_static_com_hongjingwh_lp_LpRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.github.cret.web.lineLoad.grpc.generate.LpRequest.class, com.github.cret.web.lineLoad.grpc.generate.LpRequest.Builder.class);
    }

    // Construct using com.github.cret.web.lineLoad.grpc.generate.LpRequest.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (ordersBuilder_ == null) {
        orders_ = java.util.Collections.emptyList();
      } else {
        orders_ = null;
        ordersBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      if (ctsBuilder_ == null) {
        cts_ = java.util.Collections.emptyList();
      } else {
        cts_ = null;
        ctsBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      if (productionTimesBuilder_ == null) {
        productionTimes_ = java.util.Collections.emptyList();
      } else {
        productionTimes_ = null;
        productionTimesBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.internal_static_com_hongjingwh_lp_LpRequest_descriptor;
    }

    @java.lang.Override
    public com.github.cret.web.lineLoad.grpc.generate.LpRequest getDefaultInstanceForType() {
      return com.github.cret.web.lineLoad.grpc.generate.LpRequest.getDefaultInstance();
    }

    @java.lang.Override
    public com.github.cret.web.lineLoad.grpc.generate.LpRequest build() {
      com.github.cret.web.lineLoad.grpc.generate.LpRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.github.cret.web.lineLoad.grpc.generate.LpRequest buildPartial() {
      com.github.cret.web.lineLoad.grpc.generate.LpRequest result = new com.github.cret.web.lineLoad.grpc.generate.LpRequest(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.github.cret.web.lineLoad.grpc.generate.LpRequest result) {
      if (ordersBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          orders_ = java.util.Collections.unmodifiableList(orders_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.orders_ = orders_;
      } else {
        result.orders_ = ordersBuilder_.build();
      }
      if (ctsBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          cts_ = java.util.Collections.unmodifiableList(cts_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.cts_ = cts_;
      } else {
        result.cts_ = ctsBuilder_.build();
      }
      if (productionTimesBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          productionTimes_ = java.util.Collections.unmodifiableList(productionTimes_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.productionTimes_ = productionTimes_;
      } else {
        result.productionTimes_ = productionTimesBuilder_.build();
      }
    }

    private void buildPartial0(com.github.cret.web.lineLoad.grpc.generate.LpRequest result) {
      int from_bitField0_ = bitField0_;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.github.cret.web.lineLoad.grpc.generate.LpRequest) {
        return mergeFrom((com.github.cret.web.lineLoad.grpc.generate.LpRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.github.cret.web.lineLoad.grpc.generate.LpRequest other) {
      if (other == com.github.cret.web.lineLoad.grpc.generate.LpRequest.getDefaultInstance()) return this;
      if (ordersBuilder_ == null) {
        if (!other.orders_.isEmpty()) {
          if (orders_.isEmpty()) {
            orders_ = other.orders_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureOrdersIsMutable();
            orders_.addAll(other.orders_);
          }
          onChanged();
        }
      } else {
        if (!other.orders_.isEmpty()) {
          if (ordersBuilder_.isEmpty()) {
            ordersBuilder_.dispose();
            ordersBuilder_ = null;
            orders_ = other.orders_;
            bitField0_ = (bitField0_ & ~0x00000001);
            ordersBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getOrdersFieldBuilder() : null;
          } else {
            ordersBuilder_.addAllMessages(other.orders_);
          }
        }
      }
      if (ctsBuilder_ == null) {
        if (!other.cts_.isEmpty()) {
          if (cts_.isEmpty()) {
            cts_ = other.cts_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureCtsIsMutable();
            cts_.addAll(other.cts_);
          }
          onChanged();
        }
      } else {
        if (!other.cts_.isEmpty()) {
          if (ctsBuilder_.isEmpty()) {
            ctsBuilder_.dispose();
            ctsBuilder_ = null;
            cts_ = other.cts_;
            bitField0_ = (bitField0_ & ~0x00000002);
            ctsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getCtsFieldBuilder() : null;
          } else {
            ctsBuilder_.addAllMessages(other.cts_);
          }
        }
      }
      if (productionTimesBuilder_ == null) {
        if (!other.productionTimes_.isEmpty()) {
          if (productionTimes_.isEmpty()) {
            productionTimes_ = other.productionTimes_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureProductionTimesIsMutable();
            productionTimes_.addAll(other.productionTimes_);
          }
          onChanged();
        }
      } else {
        if (!other.productionTimes_.isEmpty()) {
          if (productionTimesBuilder_.isEmpty()) {
            productionTimesBuilder_.dispose();
            productionTimesBuilder_ = null;
            productionTimes_ = other.productionTimes_;
            bitField0_ = (bitField0_ & ~0x00000004);
            productionTimesBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getProductionTimesFieldBuilder() : null;
          } else {
            productionTimesBuilder_.addAllMessages(other.productionTimes_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.github.cret.web.lineLoad.grpc.generate.Order m =
                  input.readMessage(
                      com.github.cret.web.lineLoad.grpc.generate.Order.parser(),
                      extensionRegistry);
              if (ordersBuilder_ == null) {
                ensureOrdersIsMutable();
                orders_.add(m);
              } else {
                ordersBuilder_.addMessage(m);
              }
              break;
            } // case 10
            case 18: {
              com.github.cret.web.lineLoad.grpc.generate.Ct m =
                  input.readMessage(
                      com.github.cret.web.lineLoad.grpc.generate.Ct.parser(),
                      extensionRegistry);
              if (ctsBuilder_ == null) {
                ensureCtsIsMutable();
                cts_.add(m);
              } else {
                ctsBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 26: {
              com.github.cret.web.lineLoad.grpc.generate.ProductionTime m =
                  input.readMessage(
                      com.github.cret.web.lineLoad.grpc.generate.ProductionTime.parser(),
                      extensionRegistry);
              if (productionTimesBuilder_ == null) {
                ensureProductionTimesIsMutable();
                productionTimes_.add(m);
              } else {
                productionTimesBuilder_.addMessage(m);
              }
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<com.github.cret.web.lineLoad.grpc.generate.Order> orders_ =
      java.util.Collections.emptyList();
    private void ensureOrdersIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        orders_ = new java.util.ArrayList<com.github.cret.web.lineLoad.grpc.generate.Order>(orders_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.github.cret.web.lineLoad.grpc.generate.Order, com.github.cret.web.lineLoad.grpc.generate.Order.Builder, com.github.cret.web.lineLoad.grpc.generate.OrderOrBuilder> ordersBuilder_;

    /**
     * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
     */
    public java.util.List<com.github.cret.web.lineLoad.grpc.generate.Order> getOrdersList() {
      if (ordersBuilder_ == null) {
        return java.util.Collections.unmodifiableList(orders_);
      } else {
        return ordersBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
     */
    public int getOrdersCount() {
      if (ordersBuilder_ == null) {
        return orders_.size();
      } else {
        return ordersBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
     */
    public com.github.cret.web.lineLoad.grpc.generate.Order getOrders(int index) {
      if (ordersBuilder_ == null) {
        return orders_.get(index);
      } else {
        return ordersBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
     */
    public Builder setOrders(
        int index, com.github.cret.web.lineLoad.grpc.generate.Order value) {
      if (ordersBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOrdersIsMutable();
        orders_.set(index, value);
        onChanged();
      } else {
        ordersBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
     */
    public Builder setOrders(
        int index, com.github.cret.web.lineLoad.grpc.generate.Order.Builder builderForValue) {
      if (ordersBuilder_ == null) {
        ensureOrdersIsMutable();
        orders_.set(index, builderForValue.build());
        onChanged();
      } else {
        ordersBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
     */
    public Builder addOrders(com.github.cret.web.lineLoad.grpc.generate.Order value) {
      if (ordersBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOrdersIsMutable();
        orders_.add(value);
        onChanged();
      } else {
        ordersBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
     */
    public Builder addOrders(
        int index, com.github.cret.web.lineLoad.grpc.generate.Order value) {
      if (ordersBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOrdersIsMutable();
        orders_.add(index, value);
        onChanged();
      } else {
        ordersBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
     */
    public Builder addOrders(
        com.github.cret.web.lineLoad.grpc.generate.Order.Builder builderForValue) {
      if (ordersBuilder_ == null) {
        ensureOrdersIsMutable();
        orders_.add(builderForValue.build());
        onChanged();
      } else {
        ordersBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
     */
    public Builder addOrders(
        int index, com.github.cret.web.lineLoad.grpc.generate.Order.Builder builderForValue) {
      if (ordersBuilder_ == null) {
        ensureOrdersIsMutable();
        orders_.add(index, builderForValue.build());
        onChanged();
      } else {
        ordersBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
     */
    public Builder addAllOrders(
        java.lang.Iterable<? extends com.github.cret.web.lineLoad.grpc.generate.Order> values) {
      if (ordersBuilder_ == null) {
        ensureOrdersIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, orders_);
        onChanged();
      } else {
        ordersBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
     */
    public Builder clearOrders() {
      if (ordersBuilder_ == null) {
        orders_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        ordersBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
     */
    public Builder removeOrders(int index) {
      if (ordersBuilder_ == null) {
        ensureOrdersIsMutable();
        orders_.remove(index);
        onChanged();
      } else {
        ordersBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
     */
    public com.github.cret.web.lineLoad.grpc.generate.Order.Builder getOrdersBuilder(
        int index) {
      return getOrdersFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
     */
    public com.github.cret.web.lineLoad.grpc.generate.OrderOrBuilder getOrdersOrBuilder(
        int index) {
      if (ordersBuilder_ == null) {
        return orders_.get(index);  } else {
        return ordersBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
     */
    public java.util.List<? extends com.github.cret.web.lineLoad.grpc.generate.OrderOrBuilder> 
         getOrdersOrBuilderList() {
      if (ordersBuilder_ != null) {
        return ordersBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(orders_);
      }
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
     */
    public com.github.cret.web.lineLoad.grpc.generate.Order.Builder addOrdersBuilder() {
      return getOrdersFieldBuilder().addBuilder(
          com.github.cret.web.lineLoad.grpc.generate.Order.getDefaultInstance());
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
     */
    public com.github.cret.web.lineLoad.grpc.generate.Order.Builder addOrdersBuilder(
        int index) {
      return getOrdersFieldBuilder().addBuilder(
          index, com.github.cret.web.lineLoad.grpc.generate.Order.getDefaultInstance());
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
     */
    public java.util.List<com.github.cret.web.lineLoad.grpc.generate.Order.Builder> 
         getOrdersBuilderList() {
      return getOrdersFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.github.cret.web.lineLoad.grpc.generate.Order, com.github.cret.web.lineLoad.grpc.generate.Order.Builder, com.github.cret.web.lineLoad.grpc.generate.OrderOrBuilder> 
        getOrdersFieldBuilder() {
      if (ordersBuilder_ == null) {
        ordersBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.github.cret.web.lineLoad.grpc.generate.Order, com.github.cret.web.lineLoad.grpc.generate.Order.Builder, com.github.cret.web.lineLoad.grpc.generate.OrderOrBuilder>(
                orders_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        orders_ = null;
      }
      return ordersBuilder_;
    }

    private java.util.List<com.github.cret.web.lineLoad.grpc.generate.Ct> cts_ =
      java.util.Collections.emptyList();
    private void ensureCtsIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        cts_ = new java.util.ArrayList<com.github.cret.web.lineLoad.grpc.generate.Ct>(cts_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.github.cret.web.lineLoad.grpc.generate.Ct, com.github.cret.web.lineLoad.grpc.generate.Ct.Builder, com.github.cret.web.lineLoad.grpc.generate.CtOrBuilder> ctsBuilder_;

    /**
     * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
     */
    public java.util.List<com.github.cret.web.lineLoad.grpc.generate.Ct> getCtsList() {
      if (ctsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(cts_);
      } else {
        return ctsBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
     */
    public int getCtsCount() {
      if (ctsBuilder_ == null) {
        return cts_.size();
      } else {
        return ctsBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
     */
    public com.github.cret.web.lineLoad.grpc.generate.Ct getCts(int index) {
      if (ctsBuilder_ == null) {
        return cts_.get(index);
      } else {
        return ctsBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
     */
    public Builder setCts(
        int index, com.github.cret.web.lineLoad.grpc.generate.Ct value) {
      if (ctsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCtsIsMutable();
        cts_.set(index, value);
        onChanged();
      } else {
        ctsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
     */
    public Builder setCts(
        int index, com.github.cret.web.lineLoad.grpc.generate.Ct.Builder builderForValue) {
      if (ctsBuilder_ == null) {
        ensureCtsIsMutable();
        cts_.set(index, builderForValue.build());
        onChanged();
      } else {
        ctsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
     */
    public Builder addCts(com.github.cret.web.lineLoad.grpc.generate.Ct value) {
      if (ctsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCtsIsMutable();
        cts_.add(value);
        onChanged();
      } else {
        ctsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
     */
    public Builder addCts(
        int index, com.github.cret.web.lineLoad.grpc.generate.Ct value) {
      if (ctsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCtsIsMutable();
        cts_.add(index, value);
        onChanged();
      } else {
        ctsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
     */
    public Builder addCts(
        com.github.cret.web.lineLoad.grpc.generate.Ct.Builder builderForValue) {
      if (ctsBuilder_ == null) {
        ensureCtsIsMutable();
        cts_.add(builderForValue.build());
        onChanged();
      } else {
        ctsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
     */
    public Builder addCts(
        int index, com.github.cret.web.lineLoad.grpc.generate.Ct.Builder builderForValue) {
      if (ctsBuilder_ == null) {
        ensureCtsIsMutable();
        cts_.add(index, builderForValue.build());
        onChanged();
      } else {
        ctsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
     */
    public Builder addAllCts(
        java.lang.Iterable<? extends com.github.cret.web.lineLoad.grpc.generate.Ct> values) {
      if (ctsBuilder_ == null) {
        ensureCtsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, cts_);
        onChanged();
      } else {
        ctsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
     */
    public Builder clearCts() {
      if (ctsBuilder_ == null) {
        cts_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        ctsBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
     */
    public Builder removeCts(int index) {
      if (ctsBuilder_ == null) {
        ensureCtsIsMutable();
        cts_.remove(index);
        onChanged();
      } else {
        ctsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
     */
    public com.github.cret.web.lineLoad.grpc.generate.Ct.Builder getCtsBuilder(
        int index) {
      return getCtsFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
     */
    public com.github.cret.web.lineLoad.grpc.generate.CtOrBuilder getCtsOrBuilder(
        int index) {
      if (ctsBuilder_ == null) {
        return cts_.get(index);  } else {
        return ctsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
     */
    public java.util.List<? extends com.github.cret.web.lineLoad.grpc.generate.CtOrBuilder> 
         getCtsOrBuilderList() {
      if (ctsBuilder_ != null) {
        return ctsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(cts_);
      }
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
     */
    public com.github.cret.web.lineLoad.grpc.generate.Ct.Builder addCtsBuilder() {
      return getCtsFieldBuilder().addBuilder(
          com.github.cret.web.lineLoad.grpc.generate.Ct.getDefaultInstance());
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
     */
    public com.github.cret.web.lineLoad.grpc.generate.Ct.Builder addCtsBuilder(
        int index) {
      return getCtsFieldBuilder().addBuilder(
          index, com.github.cret.web.lineLoad.grpc.generate.Ct.getDefaultInstance());
    }
    /**
     * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
     */
    public java.util.List<com.github.cret.web.lineLoad.grpc.generate.Ct.Builder> 
         getCtsBuilderList() {
      return getCtsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.github.cret.web.lineLoad.grpc.generate.Ct, com.github.cret.web.lineLoad.grpc.generate.Ct.Builder, com.github.cret.web.lineLoad.grpc.generate.CtOrBuilder> 
        getCtsFieldBuilder() {
      if (ctsBuilder_ == null) {
        ctsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.github.cret.web.lineLoad.grpc.generate.Ct, com.github.cret.web.lineLoad.grpc.generate.Ct.Builder, com.github.cret.web.lineLoad.grpc.generate.CtOrBuilder>(
                cts_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        cts_ = null;
      }
      return ctsBuilder_;
    }

    private java.util.List<com.github.cret.web.lineLoad.grpc.generate.ProductionTime> productionTimes_ =
      java.util.Collections.emptyList();
    private void ensureProductionTimesIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        productionTimes_ = new java.util.ArrayList<com.github.cret.web.lineLoad.grpc.generate.ProductionTime>(productionTimes_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.github.cret.web.lineLoad.grpc.generate.ProductionTime, com.github.cret.web.lineLoad.grpc.generate.ProductionTime.Builder, com.github.cret.web.lineLoad.grpc.generate.ProductionTimeOrBuilder> productionTimesBuilder_;

    /**
     * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
     */
    public java.util.List<com.github.cret.web.lineLoad.grpc.generate.ProductionTime> getProductionTimesList() {
      if (productionTimesBuilder_ == null) {
        return java.util.Collections.unmodifiableList(productionTimes_);
      } else {
        return productionTimesBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
     */
    public int getProductionTimesCount() {
      if (productionTimesBuilder_ == null) {
        return productionTimes_.size();
      } else {
        return productionTimesBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
     */
    public com.github.cret.web.lineLoad.grpc.generate.ProductionTime getProductionTimes(int index) {
      if (productionTimesBuilder_ == null) {
        return productionTimes_.get(index);
      } else {
        return productionTimesBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
     */
    public Builder setProductionTimes(
        int index, com.github.cret.web.lineLoad.grpc.generate.ProductionTime value) {
      if (productionTimesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureProductionTimesIsMutable();
        productionTimes_.set(index, value);
        onChanged();
      } else {
        productionTimesBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
     */
    public Builder setProductionTimes(
        int index, com.github.cret.web.lineLoad.grpc.generate.ProductionTime.Builder builderForValue) {
      if (productionTimesBuilder_ == null) {
        ensureProductionTimesIsMutable();
        productionTimes_.set(index, builderForValue.build());
        onChanged();
      } else {
        productionTimesBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
     */
    public Builder addProductionTimes(com.github.cret.web.lineLoad.grpc.generate.ProductionTime value) {
      if (productionTimesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureProductionTimesIsMutable();
        productionTimes_.add(value);
        onChanged();
      } else {
        productionTimesBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
     */
    public Builder addProductionTimes(
        int index, com.github.cret.web.lineLoad.grpc.generate.ProductionTime value) {
      if (productionTimesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureProductionTimesIsMutable();
        productionTimes_.add(index, value);
        onChanged();
      } else {
        productionTimesBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
     */
    public Builder addProductionTimes(
        com.github.cret.web.lineLoad.grpc.generate.ProductionTime.Builder builderForValue) {
      if (productionTimesBuilder_ == null) {
        ensureProductionTimesIsMutable();
        productionTimes_.add(builderForValue.build());
        onChanged();
      } else {
        productionTimesBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
     */
    public Builder addProductionTimes(
        int index, com.github.cret.web.lineLoad.grpc.generate.ProductionTime.Builder builderForValue) {
      if (productionTimesBuilder_ == null) {
        ensureProductionTimesIsMutable();
        productionTimes_.add(index, builderForValue.build());
        onChanged();
      } else {
        productionTimesBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
     */
    public Builder addAllProductionTimes(
        java.lang.Iterable<? extends com.github.cret.web.lineLoad.grpc.generate.ProductionTime> values) {
      if (productionTimesBuilder_ == null) {
        ensureProductionTimesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, productionTimes_);
        onChanged();
      } else {
        productionTimesBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
     */
    public Builder clearProductionTimes() {
      if (productionTimesBuilder_ == null) {
        productionTimes_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        productionTimesBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
     */
    public Builder removeProductionTimes(int index) {
      if (productionTimesBuilder_ == null) {
        ensureProductionTimesIsMutable();
        productionTimes_.remove(index);
        onChanged();
      } else {
        productionTimesBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
     */
    public com.github.cret.web.lineLoad.grpc.generate.ProductionTime.Builder getProductionTimesBuilder(
        int index) {
      return getProductionTimesFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
     */
    public com.github.cret.web.lineLoad.grpc.generate.ProductionTimeOrBuilder getProductionTimesOrBuilder(
        int index) {
      if (productionTimesBuilder_ == null) {
        return productionTimes_.get(index);  } else {
        return productionTimesBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
     */
    public java.util.List<? extends com.github.cret.web.lineLoad.grpc.generate.ProductionTimeOrBuilder> 
         getProductionTimesOrBuilderList() {
      if (productionTimesBuilder_ != null) {
        return productionTimesBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(productionTimes_);
      }
    }
    /**
     * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
     */
    public com.github.cret.web.lineLoad.grpc.generate.ProductionTime.Builder addProductionTimesBuilder() {
      return getProductionTimesFieldBuilder().addBuilder(
          com.github.cret.web.lineLoad.grpc.generate.ProductionTime.getDefaultInstance());
    }
    /**
     * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
     */
    public com.github.cret.web.lineLoad.grpc.generate.ProductionTime.Builder addProductionTimesBuilder(
        int index) {
      return getProductionTimesFieldBuilder().addBuilder(
          index, com.github.cret.web.lineLoad.grpc.generate.ProductionTime.getDefaultInstance());
    }
    /**
     * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
     */
    public java.util.List<com.github.cret.web.lineLoad.grpc.generate.ProductionTime.Builder> 
         getProductionTimesBuilderList() {
      return getProductionTimesFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.github.cret.web.lineLoad.grpc.generate.ProductionTime, com.github.cret.web.lineLoad.grpc.generate.ProductionTime.Builder, com.github.cret.web.lineLoad.grpc.generate.ProductionTimeOrBuilder> 
        getProductionTimesFieldBuilder() {
      if (productionTimesBuilder_ == null) {
        productionTimesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.github.cret.web.lineLoad.grpc.generate.ProductionTime, com.github.cret.web.lineLoad.grpc.generate.ProductionTime.Builder, com.github.cret.web.lineLoad.grpc.generate.ProductionTimeOrBuilder>(
                productionTimes_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        productionTimes_ = null;
      }
      return productionTimesBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.hongjingwh.lp.LpRequest)
  }

  // @@protoc_insertion_point(class_scope:com.hongjingwh.lp.LpRequest)
  private static final com.github.cret.web.lineLoad.grpc.generate.LpRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.github.cret.web.lineLoad.grpc.generate.LpRequest();
  }

  public static com.github.cret.web.lineLoad.grpc.generate.LpRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<LpRequest>
      PARSER = new com.google.protobuf.AbstractParser<LpRequest>() {
    @java.lang.Override
    public LpRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<LpRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<LpRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.github.cret.web.lineLoad.grpc.generate.LpRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

