package com.github.cret.web.lineLoad.entity.vo;


public class OrderVo {
    //订单文件ID
    private String fileId;
    //订单周期（年月或周） eg.  2025-01
    private String period;
    //产品物料编码
    private String partNumber;
    //产品名称
    private String productName;
    //需求数量
    private Integer quantity;
    //平台
    private String model;

    public OrderVo(String fileId, String period, String partNumber, String productName, Integer quantity, String model) {
        this.fileId = fileId;
        this.period = period;
        this.partNumber = partNumber;
        this.productName = productName;
        this.quantity = quantity;
        this.model = model;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getPartNumber() {
        return partNumber;
    }

    public void setPartNumber(String partNumber) {
        this.partNumber = partNumber;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }
}
