// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: IntelligentSchedule.proto

// Protobuf Java Version: 3.25.1
package com.github.cret.web.lineLoad.grpc.generate;

public final class IntelligentSchedule {
  private IntelligentSchedule() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_hongjingwh_lp_Order_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_hongjingwh_lp_Order_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_hongjingwh_lp_Ct_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_hongjingwh_lp_Ct_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_hongjingwh_lp_ProductionTime_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_hongjingwh_lp_ProductionTime_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_hongjingwh_lp_Plan_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_hongjingwh_lp_Plan_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_hongjingwh_lp_LpRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_hongjingwh_lp_LpRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_hongjingwh_lp_LpResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_hongjingwh_lp_LpResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\031IntelligentSchedule.proto\022\021com.hongjin" +
      "gwh.lp\"o\n\005Order\022\n\n\002id\030\001 \001(\t\022\016\n\006period\030\002 " +
      "\001(\t\022\023\n\013part_number\030\003 \001(\t\022\024\n\014product_name" +
      "\030\004 \001(\t\022\020\n\010quantity\030\005 \001(\005\022\r\n\005model\030\006 \001(\t\"" +
      "3\n\002Ct\022\023\n\013part_number\030\001 \001(\t\022\014\n\004line\030\002 \001(\t" +
      "\022\n\n\002ct\030\003 \001(\t\"<\n\016ProductionTime\022\014\n\004line\030\001" +
      " \001(\t\022\016\n\006period\030\002 \001(\t\022\014\n\004time\030\003 \001(\005\"\244\001\n\004P" +
      "lan\022\023\n\013part_number\030\001 \001(\t\022\024\n\014product_name" +
      "\030\002 \001(\t\022\014\n\004line\030\003 \001(\t\022\024\n\014order_period\030\004 \001" +
      "(\t\022\031\n\021production_period\030\005 \001(\t\022\020\n\010quantit" +
      "y\030\006 \001(\005\022\021\n\tcost_time\030\007 \001(\t\022\r\n\005model\030\010 \001(" +
      "\t\"\226\001\n\tLpRequest\022(\n\006orders\030\001 \003(\0132\030.com.ho" +
      "ngjingwh.lp.Order\022\"\n\003cts\030\002 \003(\0132\025.com.hon" +
      "gjingwh.lp.Ct\022;\n\020production_times\030\003 \003(\0132" +
      "!.com.hongjingwh.lp.ProductionTime\"4\n\nLp" +
      "Response\022&\n\005plans\030\001 \003(\0132\027.com.hongjingwh" +
      ".lp.Plan2N\n\tLpService\022A\n\002lp\022\034.com.hongji" +
      "ngwh.lp.LpRequest\032\035.com.hongjingwh.lp.Lp" +
      "ResponseB.\n*com.github.cret.web.lineLoad" +
      ".grpc.generateP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_hongjingwh_lp_Order_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_hongjingwh_lp_Order_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_hongjingwh_lp_Order_descriptor,
        new java.lang.String[] { "Id", "Period", "PartNumber", "ProductName", "Quantity", "Model", });
    internal_static_com_hongjingwh_lp_Ct_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_hongjingwh_lp_Ct_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_hongjingwh_lp_Ct_descriptor,
        new java.lang.String[] { "PartNumber", "Line", "Ct", });
    internal_static_com_hongjingwh_lp_ProductionTime_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_hongjingwh_lp_ProductionTime_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_hongjingwh_lp_ProductionTime_descriptor,
        new java.lang.String[] { "Line", "Period", "Time", });
    internal_static_com_hongjingwh_lp_Plan_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_hongjingwh_lp_Plan_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_hongjingwh_lp_Plan_descriptor,
        new java.lang.String[] { "PartNumber", "ProductName", "Line", "OrderPeriod", "ProductionPeriod", "Quantity", "CostTime", "Model", });
    internal_static_com_hongjingwh_lp_LpRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_hongjingwh_lp_LpRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_hongjingwh_lp_LpRequest_descriptor,
        new java.lang.String[] { "Orders", "Cts", "ProductionTimes", });
    internal_static_com_hongjingwh_lp_LpResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_hongjingwh_lp_LpResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_hongjingwh_lp_LpResponse_descriptor,
        new java.lang.String[] { "Plans", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
