package com.github.cret.web.lineLoad.grpc.generate;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.58.0)",
    comments = "Source: IntelligentSchedule.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class LpServiceGrpc {

  private LpServiceGrpc() {}

  public static final java.lang.String SERVICE_NAME = "com.hongjingwh.lp.LpService";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.github.cret.web.lineLoad.grpc.generate.LpRequest,
      com.github.cret.web.lineLoad.grpc.generate.LpResponse> getLpMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "lp",
      requestType = com.github.cret.web.lineLoad.grpc.generate.LpRequest.class,
      responseType = com.github.cret.web.lineLoad.grpc.generate.LpResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.github.cret.web.lineLoad.grpc.generate.LpRequest,
      com.github.cret.web.lineLoad.grpc.generate.LpResponse> getLpMethod() {
    io.grpc.MethodDescriptor<com.github.cret.web.lineLoad.grpc.generate.LpRequest, com.github.cret.web.lineLoad.grpc.generate.LpResponse> getLpMethod;
    if ((getLpMethod = LpServiceGrpc.getLpMethod) == null) {
      synchronized (LpServiceGrpc.class) {
        if ((getLpMethod = LpServiceGrpc.getLpMethod) == null) {
          LpServiceGrpc.getLpMethod = getLpMethod =
              io.grpc.MethodDescriptor.<com.github.cret.web.lineLoad.grpc.generate.LpRequest, com.github.cret.web.lineLoad.grpc.generate.LpResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "lp"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.github.cret.web.lineLoad.grpc.generate.LpRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.github.cret.web.lineLoad.grpc.generate.LpResponse.getDefaultInstance()))
              .setSchemaDescriptor(new LpServiceMethodDescriptorSupplier("lp"))
              .build();
        }
      }
    }
    return getLpMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static LpServiceStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<LpServiceStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<LpServiceStub>() {
        @java.lang.Override
        public LpServiceStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new LpServiceStub(channel, callOptions);
        }
      };
    return LpServiceStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static LpServiceBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<LpServiceBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<LpServiceBlockingStub>() {
        @java.lang.Override
        public LpServiceBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new LpServiceBlockingStub(channel, callOptions);
        }
      };
    return LpServiceBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static LpServiceFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<LpServiceFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<LpServiceFutureStub>() {
        @java.lang.Override
        public LpServiceFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new LpServiceFutureStub(channel, callOptions);
        }
      };
    return LpServiceFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     */
    default void lp(com.github.cret.web.lineLoad.grpc.generate.LpRequest request,
        io.grpc.stub.StreamObserver<com.github.cret.web.lineLoad.grpc.generate.LpResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getLpMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service LpService.
   */
  public static abstract class LpServiceImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return LpServiceGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service LpService.
   */
  public static final class LpServiceStub
      extends io.grpc.stub.AbstractAsyncStub<LpServiceStub> {
    private LpServiceStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected LpServiceStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new LpServiceStub(channel, callOptions);
    }

    /**
     */
    public void lp(com.github.cret.web.lineLoad.grpc.generate.LpRequest request,
        io.grpc.stub.StreamObserver<com.github.cret.web.lineLoad.grpc.generate.LpResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getLpMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service LpService.
   */
  public static final class LpServiceBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<LpServiceBlockingStub> {
    private LpServiceBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected LpServiceBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new LpServiceBlockingStub(channel, callOptions);
    }

    /**
     */
    public com.github.cret.web.lineLoad.grpc.generate.LpResponse lp(com.github.cret.web.lineLoad.grpc.generate.LpRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getLpMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service LpService.
   */
  public static final class LpServiceFutureStub
      extends io.grpc.stub.AbstractFutureStub<LpServiceFutureStub> {
    private LpServiceFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected LpServiceFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new LpServiceFutureStub(channel, callOptions);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.github.cret.web.lineLoad.grpc.generate.LpResponse> lp(
        com.github.cret.web.lineLoad.grpc.generate.LpRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getLpMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_LP = 0;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_LP:
          serviceImpl.lp((com.github.cret.web.lineLoad.grpc.generate.LpRequest) request,
              (io.grpc.stub.StreamObserver<com.github.cret.web.lineLoad.grpc.generate.LpResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getLpMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.github.cret.web.lineLoad.grpc.generate.LpRequest,
              com.github.cret.web.lineLoad.grpc.generate.LpResponse>(
                service, METHODID_LP)))
        .build();
  }

  private static abstract class LpServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    LpServiceBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("LpService");
    }
  }

  private static final class LpServiceFileDescriptorSupplier
      extends LpServiceBaseDescriptorSupplier {
    LpServiceFileDescriptorSupplier() {}
  }

  private static final class LpServiceMethodDescriptorSupplier
      extends LpServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    LpServiceMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (LpServiceGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new LpServiceFileDescriptorSupplier())
              .addMethod(getLpMethod())
              .build();
        }
      }
    }
    return result;
  }
}
