package com.github.cret.web.lineLoad;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

@SpringBootApplication
@EnableAspectJAutoProxy
@ComponentScan(basePackages = "com.github.cret.web")
@EnableMongoRepositories(basePackages = "com.github.cret.web")
public class LineLoadApplication {
    public static void main(String[] args) {
        SpringApplication.run(LineLoadApplication.class, args);
    }

}
