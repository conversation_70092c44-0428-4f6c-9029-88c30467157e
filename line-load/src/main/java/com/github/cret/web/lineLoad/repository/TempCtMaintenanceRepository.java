package com.github.cret.web.lineLoad.repository;


import com.github.cret.web.lineLoad.document.TempCtMaintenance;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.Optional;


public interface TempCtMaintenanceRepository extends MongoRepository<TempCtMaintenance, String> {

    Optional<TempCtMaintenance> findByPartNumberAndLine(String partNumber, String line);
}

