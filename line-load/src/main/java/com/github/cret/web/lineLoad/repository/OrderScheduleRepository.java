package com.github.cret.web.lineLoad.repository;


import com.github.cret.web.lineLoad.document.OrderSchedule;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OrderScheduleRepository extends MongoRepository<OrderSchedule, String> {

    @Aggregation({
            "{ $match: { 'upload_date': ?0, 'work_section': ?1, 'time_period': ?2 } }",
            "{ $sort: { version: -1 } }",
            "{ $group: { _id: null, latestDoc: { $first: '$$ROOT' } } }",
            "{ $replaceRoot: { newRoot: '$latestDoc' } }"
    })
    OrderSchedule findLatestVersionDataByUploadDateAndWorkSectionAndTimePeriod(String uploadDate, String workSection,String timePeriod);

    // 判断指定日期的订单是否已完成排产
    @Aggregation({"{ $match: { upload_date: { $in: ?0 }, 'status': true } }", "{ $group: { _id: null, count: { $sum: 1 } }}"})
    Integer countForUploadDate(List<String> uploadDates);

    @Aggregation({
            "{ $match: { 'work_section': ?0, 'time_period': ?1, 'status': true } }",
            "{ $sort: { upload_date: -1, version: -1 } }",
            "{ $group: { _id: null, latestDoc: { $first: '$$ROOT' } } }",
            "{ $replaceRoot: { newRoot: '$latestDoc' } }"
    })
    OrderSchedule findLatestVersionDataByWorkSectionAndTimePeriod(String workSection,String timePeriod);
}

