// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: IntelligentSchedule.proto

// Protobuf Java Version: 3.25.1
package com.github.cret.web.lineLoad.grpc.generate;

/**
 * Protobuf type {@code com.hongjingwh.lp.ProductionTime}
 */
public final class ProductionTime extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.hongjingwh.lp.ProductionTime)
    ProductionTimeOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ProductionTime.newBuilder() to construct.
  private ProductionTime(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ProductionTime() {
    line_ = "";
    period_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ProductionTime();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.internal_static_com_hongjingwh_lp_ProductionTime_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.internal_static_com_hongjingwh_lp_ProductionTime_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.github.cret.web.lineLoad.grpc.generate.ProductionTime.class, com.github.cret.web.lineLoad.grpc.generate.ProductionTime.Builder.class);
  }

  public static final int LINE_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object line_ = "";
  /**
   * <pre>
   *产线编号
   * </pre>
   *
   * <code>string line = 1;</code>
   * @return The line.
   */
  @java.lang.Override
  public java.lang.String getLine() {
    java.lang.Object ref = line_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      line_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *产线编号
   * </pre>
   *
   * <code>string line = 1;</code>
   * @return The bytes for line.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLineBytes() {
    java.lang.Object ref = line_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      line_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PERIOD_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object period_ = "";
  /**
   * <pre>
   *生产周期 （年月或周） eg. 2025-01
   * </pre>
   *
   * <code>string period = 2;</code>
   * @return The period.
   */
  @java.lang.Override
  public java.lang.String getPeriod() {
    java.lang.Object ref = period_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      period_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *生产周期 （年月或周） eg. 2025-01
   * </pre>
   *
   * <code>string period = 2;</code>
   * @return The bytes for period.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPeriodBytes() {
    java.lang.Object ref = period_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      period_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TIME_FIELD_NUMBER = 3;
  private int time_ = 0;
  /**
   * <pre>
   *可用生产时间
   * </pre>
   *
   * <code>int32 time = 3;</code>
   * @return The time.
   */
  @java.lang.Override
  public int getTime() {
    return time_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(line_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, line_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(period_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, period_);
    }
    if (time_ != 0) {
      output.writeInt32(3, time_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(line_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, line_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(period_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, period_);
    }
    if (time_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, time_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.github.cret.web.lineLoad.grpc.generate.ProductionTime)) {
      return super.equals(obj);
    }
    com.github.cret.web.lineLoad.grpc.generate.ProductionTime other = (com.github.cret.web.lineLoad.grpc.generate.ProductionTime) obj;

    if (!getLine()
        .equals(other.getLine())) return false;
    if (!getPeriod()
        .equals(other.getPeriod())) return false;
    if (getTime()
        != other.getTime()) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + LINE_FIELD_NUMBER;
    hash = (53 * hash) + getLine().hashCode();
    hash = (37 * hash) + PERIOD_FIELD_NUMBER;
    hash = (53 * hash) + getPeriod().hashCode();
    hash = (37 * hash) + TIME_FIELD_NUMBER;
    hash = (53 * hash) + getTime();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.github.cret.web.lineLoad.grpc.generate.ProductionTime parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.ProductionTime parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.ProductionTime parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.ProductionTime parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.ProductionTime parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.ProductionTime parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.ProductionTime parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.ProductionTime parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.github.cret.web.lineLoad.grpc.generate.ProductionTime parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.github.cret.web.lineLoad.grpc.generate.ProductionTime parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.ProductionTime parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.github.cret.web.lineLoad.grpc.generate.ProductionTime parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.github.cret.web.lineLoad.grpc.generate.ProductionTime prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.hongjingwh.lp.ProductionTime}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.hongjingwh.lp.ProductionTime)
      com.github.cret.web.lineLoad.grpc.generate.ProductionTimeOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.internal_static_com_hongjingwh_lp_ProductionTime_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.internal_static_com_hongjingwh_lp_ProductionTime_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.github.cret.web.lineLoad.grpc.generate.ProductionTime.class, com.github.cret.web.lineLoad.grpc.generate.ProductionTime.Builder.class);
    }

    // Construct using com.github.cret.web.lineLoad.grpc.generate.ProductionTime.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      line_ = "";
      period_ = "";
      time_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.github.cret.web.lineLoad.grpc.generate.IntelligentSchedule.internal_static_com_hongjingwh_lp_ProductionTime_descriptor;
    }

    @java.lang.Override
    public com.github.cret.web.lineLoad.grpc.generate.ProductionTime getDefaultInstanceForType() {
      return com.github.cret.web.lineLoad.grpc.generate.ProductionTime.getDefaultInstance();
    }

    @java.lang.Override
    public com.github.cret.web.lineLoad.grpc.generate.ProductionTime build() {
      com.github.cret.web.lineLoad.grpc.generate.ProductionTime result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.github.cret.web.lineLoad.grpc.generate.ProductionTime buildPartial() {
      com.github.cret.web.lineLoad.grpc.generate.ProductionTime result = new com.github.cret.web.lineLoad.grpc.generate.ProductionTime(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.github.cret.web.lineLoad.grpc.generate.ProductionTime result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.line_ = line_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.period_ = period_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.time_ = time_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.github.cret.web.lineLoad.grpc.generate.ProductionTime) {
        return mergeFrom((com.github.cret.web.lineLoad.grpc.generate.ProductionTime)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.github.cret.web.lineLoad.grpc.generate.ProductionTime other) {
      if (other == com.github.cret.web.lineLoad.grpc.generate.ProductionTime.getDefaultInstance()) return this;
      if (!other.getLine().isEmpty()) {
        line_ = other.line_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getPeriod().isEmpty()) {
        period_ = other.period_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.getTime() != 0) {
        setTime(other.getTime());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              line_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              period_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              time_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object line_ = "";
    /**
     * <pre>
     *产线编号
     * </pre>
     *
     * <code>string line = 1;</code>
     * @return The line.
     */
    public java.lang.String getLine() {
      java.lang.Object ref = line_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        line_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *产线编号
     * </pre>
     *
     * <code>string line = 1;</code>
     * @return The bytes for line.
     */
    public com.google.protobuf.ByteString
        getLineBytes() {
      java.lang.Object ref = line_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        line_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *产线编号
     * </pre>
     *
     * <code>string line = 1;</code>
     * @param value The line to set.
     * @return This builder for chaining.
     */
    public Builder setLine(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      line_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *产线编号
     * </pre>
     *
     * <code>string line = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearLine() {
      line_ = getDefaultInstance().getLine();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *产线编号
     * </pre>
     *
     * <code>string line = 1;</code>
     * @param value The bytes for line to set.
     * @return This builder for chaining.
     */
    public Builder setLineBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      line_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object period_ = "";
    /**
     * <pre>
     *生产周期 （年月或周） eg. 2025-01
     * </pre>
     *
     * <code>string period = 2;</code>
     * @return The period.
     */
    public java.lang.String getPeriod() {
      java.lang.Object ref = period_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        period_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *生产周期 （年月或周） eg. 2025-01
     * </pre>
     *
     * <code>string period = 2;</code>
     * @return The bytes for period.
     */
    public com.google.protobuf.ByteString
        getPeriodBytes() {
      java.lang.Object ref = period_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        period_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *生产周期 （年月或周） eg. 2025-01
     * </pre>
     *
     * <code>string period = 2;</code>
     * @param value The period to set.
     * @return This builder for chaining.
     */
    public Builder setPeriod(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      period_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *生产周期 （年月或周） eg. 2025-01
     * </pre>
     *
     * <code>string period = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearPeriod() {
      period_ = getDefaultInstance().getPeriod();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <pre>
     *生产周期 （年月或周） eg. 2025-01
     * </pre>
     *
     * <code>string period = 2;</code>
     * @param value The bytes for period to set.
     * @return This builder for chaining.
     */
    public Builder setPeriodBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      period_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private int time_ ;
    /**
     * <pre>
     *可用生产时间
     * </pre>
     *
     * <code>int32 time = 3;</code>
     * @return The time.
     */
    @java.lang.Override
    public int getTime() {
      return time_;
    }
    /**
     * <pre>
     *可用生产时间
     * </pre>
     *
     * <code>int32 time = 3;</code>
     * @param value The time to set.
     * @return This builder for chaining.
     */
    public Builder setTime(int value) {

      time_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可用生产时间
     * </pre>
     *
     * <code>int32 time = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearTime() {
      bitField0_ = (bitField0_ & ~0x00000004);
      time_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.hongjingwh.lp.ProductionTime)
  }

  // @@protoc_insertion_point(class_scope:com.hongjingwh.lp.ProductionTime)
  private static final com.github.cret.web.lineLoad.grpc.generate.ProductionTime DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.github.cret.web.lineLoad.grpc.generate.ProductionTime();
  }

  public static com.github.cret.web.lineLoad.grpc.generate.ProductionTime getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ProductionTime>
      PARSER = new com.google.protobuf.AbstractParser<ProductionTime>() {
    @java.lang.Override
    public ProductionTime parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ProductionTime> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ProductionTime> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.github.cret.web.lineLoad.grpc.generate.ProductionTime getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

