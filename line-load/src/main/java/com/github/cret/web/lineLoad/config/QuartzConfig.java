package com.github.cret.web.lineLoad.config;

import com.github.cret.web.lineLoad.job.OrderCheckJob;
import org.quartz.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class QuartzConfig {

    private static final String DEPARTMENT_MONTH_COST_JOB_IDENTITY = "OrderCheckJob";
    private static final String DEPARTMENT_MONTH_COST_TRIGGER = "OrderCheckTrigger";
    private static final String DEPARTMENT_MONTH_COST_GROUP = "OrderCheckJobGroup";


    @Bean
    public JobDetail OrderCheckJobDetail() {
        return JobBuilder.newJob(OrderCheckJob.class)
                .withIdentity(DEPARTMENT_MONTH_COST_JOB_IDENTITY, DEPARTMENT_MONTH_COST_GROUP)
                .storeDurably()
                .build();
    }

    @Bean
    public Trigger OrderCheckTrigger() {
        // 每周四早上10点执行
        CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule("0 0 15 ? * THU");

        return TriggerBuilder.newTrigger()
                .forJob(OrderCheckJobDetail())
                .withIdentity(DEPARTMENT_MONTH_COST_TRIGGER, DEPARTMENT_MONTH_COST_GROUP)
                .withSchedule(scheduleBuilder)
                .build();
    }

}
