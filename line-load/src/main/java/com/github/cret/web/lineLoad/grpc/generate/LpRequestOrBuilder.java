// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: IntelligentSchedule.proto

// Protobuf Java Version: 3.25.1
package com.github.cret.web.lineLoad.grpc.generate;

public interface LpRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.hongjingwh.lp.LpRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
   */
  java.util.List<com.github.cret.web.lineLoad.grpc.generate.Order> 
      getOrdersList();
  /**
   * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
   */
  com.github.cret.web.lineLoad.grpc.generate.Order getOrders(int index);
  /**
   * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
   */
  int getOrdersCount();
  /**
   * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
   */
  java.util.List<? extends com.github.cret.web.lineLoad.grpc.generate.OrderOrBuilder> 
      getOrdersOrBuilderList();
  /**
   * <code>repeated .com.hongjingwh.lp.Order orders = 1;</code>
   */
  com.github.cret.web.lineLoad.grpc.generate.OrderOrBuilder getOrdersOrBuilder(
      int index);

  /**
   * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
   */
  java.util.List<com.github.cret.web.lineLoad.grpc.generate.Ct> 
      getCtsList();
  /**
   * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
   */
  com.github.cret.web.lineLoad.grpc.generate.Ct getCts(int index);
  /**
   * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
   */
  int getCtsCount();
  /**
   * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
   */
  java.util.List<? extends com.github.cret.web.lineLoad.grpc.generate.CtOrBuilder> 
      getCtsOrBuilderList();
  /**
   * <code>repeated .com.hongjingwh.lp.Ct cts = 2;</code>
   */
  com.github.cret.web.lineLoad.grpc.generate.CtOrBuilder getCtsOrBuilder(
      int index);

  /**
   * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
   */
  java.util.List<com.github.cret.web.lineLoad.grpc.generate.ProductionTime> 
      getProductionTimesList();
  /**
   * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
   */
  com.github.cret.web.lineLoad.grpc.generate.ProductionTime getProductionTimes(int index);
  /**
   * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
   */
  int getProductionTimesCount();
  /**
   * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
   */
  java.util.List<? extends com.github.cret.web.lineLoad.grpc.generate.ProductionTimeOrBuilder> 
      getProductionTimesOrBuilderList();
  /**
   * <code>repeated .com.hongjingwh.lp.ProductionTime production_times = 3;</code>
   */
  com.github.cret.web.lineLoad.grpc.generate.ProductionTimeOrBuilder getProductionTimesOrBuilder(
      int index);
}
