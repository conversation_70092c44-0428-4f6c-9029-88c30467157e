package com.github.cret.web.lineLoad.service.impl;


import cn.hutool.core.util.NumberUtil;
import com.github.cret.web.common.domain.FileRef;
import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.lineLoad.document.CtMaintenance;
import com.github.cret.web.lineLoad.document.OrderPeriodPlan;
import com.github.cret.web.lineLoad.document.TempCtMaintenance;
import com.github.cret.web.lineLoad.document.UploadFile;
import com.github.cret.web.lineLoad.entity.vo.CtVo;
import com.github.cret.web.lineLoad.enums.WorkSection;
import com.github.cret.web.lineLoad.repository.CtMaintenanceRepository;
import com.github.cret.web.lineLoad.repository.OrderPeriodPlanRepository;
import com.github.cret.web.lineLoad.repository.TempCtMaintenanceRepository;
import com.github.cret.web.lineLoad.repository.UploadFileRepository;
import com.github.cret.web.lineLoad.service.CtMaintenanceService;
import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.util.SecurityUtil;
import com.github.cret.web.system.service.AttachmentService;
import jakarta.annotation.Resource;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;


@Service
public class CtMaintenanceServiceImpl implements CtMaintenanceService {

    private static final Logger log = LoggerFactory.getLogger(CtMaintenanceServiceImpl.class);

    @Resource
    private CtMaintenanceRepository ctMaintenanceRepository;
    @Resource
    private OrderPeriodPlanRepository orderPeriodPlanRepository;
    @Resource
    private AttachmentService attachmentService;
    @Resource
    private UploadFileRepository uploadFileRepository;
    @Resource
    private TempCtMaintenanceRepository tempCtMaintenanceRepository;

    @Override
    public void upload(MultipartFile excel) throws IOException {

        AuthUser user = SecurityUtil.getCurrentUser();
        assert user != null;

        //保存文件到本地
        FileRef fileRef = attachmentService.upload(excel);
        UploadFile uploadFile = uploadFileRepository.save(new UploadFile(2, excel.getOriginalFilename(), fileRef.fileKey(), new Date(), user.name()));

        //解析Excel文件
        List<CtMaintenance> ctMaintenanceList = parseExcelFile(excel);

        //若临时ct录入了该产品和线体的ct，则删除临时ct中的数据
        Map<String, String> tempCtMaintenances = new HashMap<>();
        tempCtMaintenanceRepository.findAll().forEach(tempCtMaintenance -> tempCtMaintenances.put(tempCtMaintenance.getPartNumber() + "|" + tempCtMaintenance.getLine(), tempCtMaintenance.getId()));
        List<String> repeatCtMaintenances = new ArrayList<>();
        ctMaintenanceList.forEach(ctMaintenance -> {
            String key = ctMaintenance.getPartNumber() + "|" + ctMaintenance.getLine();
            if (tempCtMaintenances.containsKey(key)) {
                repeatCtMaintenances.add(key);
            }
        });
        List<String> repeatCtMaintenanceIds = new ArrayList<>();
        repeatCtMaintenances.forEach(repeatCtMaintenance -> repeatCtMaintenanceIds.add(tempCtMaintenances.get(repeatCtMaintenance)));
        tempCtMaintenanceRepository.deleteAllById(repeatCtMaintenanceIds);

        //保存到数据库
        if (!ctMaintenanceList.isEmpty()) {
            Date now = new Date();

            //找到目前最大的版本号
            int maxVersion = ctMaintenanceRepository.findMaxVersion().orElse(0);

            ctMaintenanceList.forEach(ctMaintenance -> {
                ctMaintenance.setCreator((user.name()));
                ctMaintenance.setCreateTime(now);
                ctMaintenance.setFileId(uploadFile.getId());
                ctMaintenance.setVersion(maxVersion == 0 ? 1 : maxVersion + 1);
            });
            ctMaintenanceRepository.saveAll(ctMaintenanceList);
            log.info("成功解析并保存了 {} 条CT维护数据", ctMaintenanceList.size());
        }
    }

    @Override
    public List<CtVo> getOrderCt(String uploadDate, String workSection) {
        List<OrderPeriodPlan> orderPeriodPlans = orderPeriodPlanRepository.findByUploadDate(uploadDate);
        List<String> partNumbers = orderPeriodPlans.stream().map(OrderPeriodPlan::getPartNumber).toList();
        List<CtMaintenance> ctMaintenances = ctMaintenanceRepository.findByWorkSection(workSection);
        // 找出订单中，需要该工段的产品号的最大CT值
        List<CtVo> ctVos = new ArrayList<>(ctMaintenances.stream().filter(ctMaintenance -> partNumbers.contains(ctMaintenance.getPartNumber()))
                .map(ctMaintenance ->
                        new CtVo(ctMaintenance.getPartNumber(), ctMaintenance.getLine(), workSection, ctMaintenance.getMaxCt(), ctMaintenance.getBoard(), ctMaintenance.getModel()))
                .toList());
        // 对于SMT段的ct，需要合并B、T面的CT时间
        if (workSection.equals(WorkSection.SMT.name())) {
            Map<String, CtVo> mergedSmtCtMap = new HashMap<>();
            ctVos.forEach(ctVo -> {
                String key = ctVo.getLine() + ":" + ctVo.getPartNumber();
                if (mergedSmtCtMap.containsKey(key)) {
                    CtVo existing = mergedSmtCtMap.get(key);
                    // 如果已有记录，合并 CT 时间
                    existing.setCt(NumberUtil
                            .roundStr(Double.parseDouble(existing.getCt()) + Double.parseDouble(ctVo.getCt()), 2));
                    existing.setBoard("");
                } else {
                    // 新建记录
                    mergedSmtCtMap.put(key, ctVo);
                }
            });
            List<CtVo> smtCtVos = new ArrayList<>(mergedSmtCtMap.values());
            List<CtVo> dipCtVos = ctVos.stream().filter(ctVo -> ctVo.getWorkSection().equals(WorkSection.DIP.name()))
                    .toList();
            ctVos.clear();
            ctVos.addAll(smtCtVos);
            ctVos.addAll(dipCtVos);
        }
        return ctVos;
    }

    @Override
    public List<CtVo> listAllCt() {
        List<CtMaintenance> ctMaintenances = ctMaintenanceRepository.findLatestVersionData();
        // 再查出临时ct，合并返回
        List<TempCtMaintenance> tempCtMaintenances = tempCtMaintenanceRepository.findAll();
        List<CtVo> ctVos = new ArrayList<>(ctMaintenances.stream().map(ctMaintenance ->
                new CtVo(ctMaintenance.getPartNumber(), ctMaintenance.getLine(), ctMaintenance.getWorkSection(), ctMaintenance.getMaxCt(), ctMaintenance.getBoard(), ctMaintenance.getModel())).toList());
        List<CtVo> tempCtVos = tempCtMaintenances.stream().map(tempCtMaintenance ->
                new CtVo(tempCtMaintenance.getPartNumber(), tempCtMaintenance.getLine(), tempCtMaintenance.getWorkSection(), tempCtMaintenance.getCt(), "", tempCtMaintenance.getModel())).toList();
        ctVos.addAll(tempCtVos);
        // 对于SMT段的ct，需要合并B、T面的CT时间
        Map<String, CtVo> mergedSmtCtMap = new HashMap<>();
        ctVos.stream()
                .filter(ctVo -> WorkSection.SMT.name().equals(ctVo.getWorkSection()))
                .forEach(ctVo -> {
                    String key = ctVo.getLine() + ":" + ctVo.getPartNumber();
                    if (mergedSmtCtMap.containsKey(key)) {
                        CtVo existing = mergedSmtCtMap.get(key);
                        // 如果已有记录，合并 CT 时间
                        existing.setCt(NumberUtil
                                .roundStr(Double.parseDouble(existing.getCt()) + Double.parseDouble(ctVo.getCt()), 2));
                        existing.setBoard("");
                    } else {
                        // 新建记录
                        mergedSmtCtMap.put(key, ctVo);
                    }
                });
        List<CtVo> smtCtVos = new ArrayList<>(mergedSmtCtMap.values());
        List<CtVo> dipCtVos = ctVos.stream().filter(ctVo -> ctVo.getWorkSection().equals(WorkSection.DIP.name()))
                .toList();
        ctVos.clear();
        ctVos.addAll(smtCtVos);
        ctVos.addAll(dipCtVos);
        return ctVos;
    }

    @Override
    public List<CtVo> list() {
        List<CtMaintenance> ctMaintenances = ctMaintenanceRepository.findLatestVersionData();
        return new ArrayList<>(ctMaintenances.stream().map(ctMaintenance ->
                new CtVo(ctMaintenance.getPartNumber(), ctMaintenance.getLine(), ctMaintenance.getWorkSection(), ctMaintenance.getMaxCt(), ctMaintenance.getBoard(), ctMaintenance.getModel())).toList());
    }

    /**
     * 解析Excel文件
     */
    private List<CtMaintenance> parseExcelFile(MultipartFile excel) {
        List<CtMaintenance> result = new ArrayList<>();

        try (Workbook workbook = WorkbookFactory.create(excel.getInputStream())) {
            // 解析SMT段
            Sheet smtSheet = workbook.getSheet("SMT段");
            if (smtSheet != null) {
                result.addAll(parseSmtSheet(smtSheet));
            } else {
                log.error("未找到SMT段工作表");
            }

            // 解析DIP段
            Sheet dipSheet = workbook.getSheet("DIP段");
            if (dipSheet != null) {
                result.addAll(parseDipSheet(dipSheet));
            } else {
                log.error("未找到DIP段工作表");
            }
        } catch (Exception e) {
            log.error("解析Excel文件失败", e);
            throw SysErrEnum.UPLOAD_FILE_FAIL.exception("未找到排班计划");
        }

        return result;
    }

    /**
     * 解析SMT段工作表
     * 根据真实Excel格式优化，处理合并单元格和复杂表头
     */
    private List<CtMaintenance> parseSmtSheet(Sheet sheet) {
        List<CtMaintenance> result = new ArrayList<>();

        // 查找表头行和数据开始行
        int headerRowIndex = findHeaderRow(sheet);
        int dataStartRow = headerRowIndex + 2;

        if (headerRowIndex == -1) {
            log.error("SMT段工作表中未找到表头行");
            return new ArrayList<>();
        }

        // 解析表头，找到各列的位置
        Map<String, Integer> columnMap = parseSmtHeaders(sheet, headerRowIndex);

        // 用于处理合并单元格的值
        String lastLine = "";
        String lastModel = "";

        // 从数据行开始解析
        for (int i = dataStartRow; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;

            // 获取基本信息，处理合并单元格
            String line = getCellStringValue(row.getCell(columnMap.getOrDefault("线体", 0)));
            String partNumber = getCellStringValue(row.getCell(columnMap.getOrDefault("组合号", 1)));
            String model = getCellStringValue(row.getCell(columnMap.getOrDefault("平台", 2)));
            Double maxCtDouble = getCellDoubleValue(row.getCell(columnMap.getOrDefault("CT", 3)));
            if (null == maxCtDouble) {
                continue;
            }
            String maxCt = NumberUtil.roundStr(maxCtDouble, 2);
            String board = getCellStringValue(row.getCell(columnMap.getOrDefault("版面", 4)));

            // 跳过组合号或CT为空或0的行
            if (!StringUtils.hasText(partNumber) || maxCt == null || maxCt.equals("0.00")) {
                continue;
            }

            // 处理合并单元格：如果当前行的值为空，使用上一行的值
            if (StringUtils.hasText(line)) {
                lastLine = line;
            } else {
                line = lastLine;
            }

            if (StringUtils.hasText(model)) {
                lastModel = model;
            } else {
                model = lastModel;
            }

            CtMaintenance ctMaintenance = new CtMaintenance();
            ctMaintenance.setLine(line);
            ctMaintenance.setPartNumber(partNumber);
            ctMaintenance.setModel(model);
            ctMaintenance.setMaxCt(maxCt);
            ctMaintenance.setWorkSection(WorkSection.SMT.name());
            ctMaintenance.setBoard(board);
            ctMaintenance.setProcedure(new ArrayList<>());
            result.add(ctMaintenance);

            // 解析工序数据
            List<CtMaintenance.Procedure> procedures = ctMaintenance.getProcedure();
            parseSmtProcedures(sheet, headerRowIndex, row, procedures);
        }

        return result;
    }

    /**
     * 解析SMT段表头，找到各列的位置
     */
    private Map<String, Integer> parseSmtHeaders(Sheet sheet, int headerRowIndex) {
        Map<String, Integer> columnMap = new HashMap<>();
        Row headerRow = sheet.getRow(headerRowIndex);

        if (headerRow != null) {
            for (int i = 0; i < 24; i++) {
                Cell cell = headerRow.getCell(i);
                if (cell != null) {
                    String headerValue = getCellStringValue(cell);
                    if (StringUtils.hasText(headerValue)) {
                        // 根据表头内容映射列位置
                        if (headerValue.equals("线体")) {
                            columnMap.put("线体", i);
                        } else if (headerValue.equals("组合号")) {
                            columnMap.put("组合号", i);
                        } else if (headerValue.equals("平台")) {
                            columnMap.put("平台", i);
                        } else if (headerValue.contains("（单位：s）")) {
                            columnMap.put("CT", i);
                        } else if (headerValue.equals("版面")) {
                            columnMap.put("版面", i);
                        }
                    }
                }
            }
        }

        return columnMap;
    }

    /**
     * 解析SMT段工序数据
     */
    private void parseSmtProcedures(Sheet sheet, int headerRowIndex, Row dataRow, List<CtMaintenance.Procedure> procedures) {
        Row procedureNameRow = sheet.getRow(headerRowIndex + 1);
        if (procedureNameRow == null) return;

        // 从第11列开始查找工序数据（根据实际Excel调整）
        for (int colIndex = 10; colIndex < 23; colIndex++) {
            Cell nameCell = procedureNameRow.getCell(colIndex);
            Cell dataCell = dataRow.getCell(colIndex);

            if (nameCell != null && dataCell != null) {
                String procedureName = getCellStringValue(nameCell);
                Double procedureCt = getCellDoubleValue(dataCell);

                // 过滤掉非工序列（如CT总计、备注等）
                if (StringUtils.hasText(procedureName) &&
                        !procedureName.contains("CT") &&
                        !procedureName.contains("总计") &&
                        !procedureName.contains("备注") &&
                        !procedureName.contains("UPH") &&
                        procedureCt != null && procedureCt > 0) {
                    procedures.add(new CtMaintenance.Procedure(procedureName, procedureCt));
                }
            }
        }
    }

    /**
     * 解析DIP段工作表（PCBA段）
     * 根据真实Excel格式优化，处理合并单元格和复杂表头
     */
    private List<CtMaintenance> parseDipSheet(Sheet sheet) {
        List<CtMaintenance> result = new ArrayList<>();

        // 查找表头行和数据开始行
        int headerRowIndex = findHeaderRow(sheet);
        int dataStartRow = headerRowIndex + 2;

        if (headerRowIndex == -1) {
            log.error("DIP段工作表中未找到表头行");
            return new ArrayList<>();
        }

        // 解析表头，找到各列的位置
        Map<String, Integer> columnMap = parseDipHeaders(sheet, headerRowIndex);

        // 用于处理合并单元格的值
        String lastLine = "";
        String lastModel = "";

        // 从数据行开始解析，每次步长为三行，因为一个产品号的数据占用了三行
        for (int i = dataStartRow; i <= sheet.getLastRowNum(); i += 3) {
            Row row = sheet.getRow(i);
            if (row == null) continue;

            // 获取基本信息，处理合并单元格
            String line = getCellStringValue(row.getCell(columnMap.getOrDefault("线体", 0)));
            String partNumber = getCellStringValue(row.getCell(columnMap.getOrDefault("组合号", 1)));
            String model = getCellStringValue(row.getCell(columnMap.getOrDefault("平台", 2)));
            Double maxCtDouble = getCellDoubleValue(row.getCell(columnMap.getOrDefault("CT", 3)));
            if (null == maxCtDouble) {
                continue;
            }
            String maxCt = NumberUtil.roundStr(maxCtDouble, 2);
            // 跳过组合号或CT为空或0的行
            if (!StringUtils.hasText(partNumber) || maxCt == null || maxCt.equals("0.00")) {
                continue;
            }

            // 处理合并单元格：如果当前行的值为空，使用上一行的值
            if (StringUtils.hasText(line)) {
                lastLine = line;
            } else {
                line = lastLine;
            }

            if (StringUtils.hasText(model)) {
                lastModel = model;
            } else {
                model = lastModel;
            }

            CtMaintenance ctMaintenance = new CtMaintenance();
            ctMaintenance.setLine(line);
            ctMaintenance.setPartNumber(partNumber);
            ctMaintenance.setModel(model);
            ctMaintenance.setMaxCt(maxCt);
            ctMaintenance.setWorkSection(WorkSection.DIP.name());
            ctMaintenance.setProcedure(new ArrayList<>());
            result.add(ctMaintenance);

            // 解析工序数据
            List<CtMaintenance.Procedure> procedures = ctMaintenance.getProcedure();
            parseDipProcedures(sheet, row, procedures);
        }

        return result;
    }

    /**
     * 解析DIP段表头，找到各列的位置
     */
    private Map<String, Integer> parseDipHeaders(Sheet sheet, int headerRowIndex) {
        Map<String, Integer> columnMap = new HashMap<>();
        Row headerRow = sheet.getRow(headerRowIndex);

        if (headerRow != null) {
//            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            for (int i = 0; i < 28; i++) {
                Cell cell = headerRow.getCell(i);
                if (cell != null) {
                    String headerValue = getCellStringValue(cell);
                    if (StringUtils.hasText(headerValue)) {
                        // 根据表头内容映射列位置
                        if (headerValue.equals("线体")) {
                            columnMap.put("线体", i);
                        } else if (headerValue.equals("组合号")) {
                            columnMap.put("组合号", i);
                        } else if (headerValue.equals("平台")) {
                            columnMap.put("平台", i);
                        } else if (headerValue.contains("（单位：s）")) {
                            columnMap.put("CT", i);
                        }
                    }
                }
            }
        }

        return columnMap;
    }

    /**
     * 解析DIP段工序数据
     */
    private void parseDipProcedures(Sheet sheet, Row procedureNameRow, List<CtMaintenance.Procedure> procedures) {
        Row ctRow = sheet.getRow(procedureNameRow.getRowNum() + 1);
        // 从第8列开始查找工序数据（根据实际Excel调整）
        for (int colIndex = 7; colIndex < procedureNameRow.getLastCellNum(); colIndex++) {
            Cell dataCell = procedureNameRow.getCell(colIndex);
            Cell ctCell = ctRow.getCell(colIndex);
            if (dataCell != null) {
                String procedureName = getCellStringValue(dataCell);
                Double procedureCt = getCellDoubleValue(ctCell);

                // 过滤掉非工序列（如CT总计、备注等）
                if (StringUtils.hasText(procedureName) &&
                        !procedureName.contains("CT") &&
                        !procedureName.contains("总计") &&
                        !procedureName.contains("备注") &&
                        !procedureName.contains("UPH") &&
                        !procedureName.contains("合计") &&
                        procedureCt != null && procedureCt > 0) {

                    procedures.add(new CtMaintenance.Procedure(procedureName, procedureCt));
                }
            }
        }
    }

    /**
     * 查找包含指定关键字的表头行
     */
    private int findHeaderRow(Sheet sheet) {
        for (int i = 0; i <= Math.min(10, sheet.getLastRowNum()); i++) { // 只在前10行中查找
            Row row = sheet.getRow(i);
            if (row != null) {
                for (int j = 0; j < row.getLastCellNum(); j++) {
                    Cell cell = row.getCell(j);
                    if (cell != null) {
                        String cellValue = getCellStringValue(cell);
                        if (StringUtils.hasText(cellValue) && cellValue.contains("线体")) {
                            return i;
                        }
                    }
                }
            }
        }
        return -1; // 未找到
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 避免科学计数法显示
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue().trim();
                } catch (Exception e) {
                    try {
                        return String.valueOf(cell.getNumericCellValue());
                    } catch (Exception ex) {
                        return null;
                    }
                }
            default:
                return null;
        }
    }

    /**
     * 获取单元格数值
     */
    private Double getCellDoubleValue(Cell cell) {
        if (cell == null) {
            return 0.0;
        }

        switch (cell.getCellType()) {
            case NUMERIC:
                return cell.getNumericCellValue();
            case STRING:
                String stringValue = cell.getStringCellValue().trim();
                if (StringUtils.hasText(stringValue)) {
                    try {
                        return Double.parseDouble(stringValue);
                    } catch (NumberFormatException e) {
                        return 0.0;
                    }
                }
                return 0.0;
            case FORMULA:
                try {
                    return cell.getNumericCellValue();
                } catch (Exception e) {
                    try {
                        String formulaStringValue = cell.getStringCellValue().trim();
                        return StringUtils.hasText(formulaStringValue) ? Double.parseDouble(formulaStringValue) : null;
                    } catch (Exception ex) {
                        return 0.0;
                    }
                }
            default:
                return 0.0;
        }
    }


}

