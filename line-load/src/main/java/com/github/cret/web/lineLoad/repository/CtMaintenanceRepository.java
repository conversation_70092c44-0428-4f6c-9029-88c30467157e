package com.github.cret.web.lineLoad.repository;


import com.github.cret.web.lineLoad.document.CtMaintenance;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface CtMaintenanceRepository extends MongoRepository<CtMaintenance, String> {

    @Aggregation({
            "{ $match: { work_section: ?0} }",
            "{ $sort: { version: -1 } }",
            "{ $group: { _id: null, maxVersion: { $first: '$version' }, docs: { $push: '$$ROOT' } }}",
            "{ $project: {items: {$filter: { input: '$docs',as: 'doc', cond: { $eq: [ '$$doc.version', '$maxVersion' ] }}}}}",
            "{ $unwind: '$items' }",
            "{ $replaceRoot: { newRoot: '$items' }}",
    })
    List<CtMaintenance> findByWorkSection(String workSection);

    @Aggregation("{ $group: { _id: null, maxVersion: { $max: '$version' } } }")
    Optional<Integer> findMaxVersion();

    //找出版本号最大的所有记录
    @Aggregation({
            "{ $sort: { version: -1 } }",
            "{ $group: { _id: null, maxVersion: { $first: '$version' }, docs: { $push: '$$ROOT' } }}",
            "{ $project: {items: {$filter: { input: '$docs',as: 'doc', cond: { $eq: [ '$$doc.version', '$maxVersion' ] }}}}}",
            "{ $unwind: '$items' }",
            "{ $replaceRoot: { newRoot: '$items' }}",
            "{ $sort: { line: 1 } }",
    })
    List<CtMaintenance> findLatestVersionData();

    @Aggregation({
            "{ $match: { part_number: ?0, line: ?1 } }",
            "{ $sort: { version: -1 } }",
            "{ $group: { _id: null, maxVersion: { $first: '$version' }, docs: { $push: '$$ROOT' } }}",
            "{ $project: {items: {$filter: { input: '$docs',as: 'doc', cond: { $eq: [ '$$doc.version', '$maxVersion' ] }}}}}",
            "{ $unwind: '$items' }",
            "{ $replaceRoot: { newRoot: '$items' }}",
            "{ $sort: { line: 1 } }",
            "{ $count: part_number }",
    })
    Optional<Long> countByPartNumberAndLine(String partNumber, String line);

    List<CtMaintenance> findByLineAndPartNumberAndCreateTimeBetween(String line, String partNumber, Date createTimeAfter, Date createTimeBefore);
}

