package com.github.cret.web.lineLoad.document;


import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;
import java.util.List;

/**
 * Description: 订单排班详情
 * date: 2025/7/9 10:19
 *
 * @author: zxk
 */
@Document("order_schedule")
public class OrderSchedule {

    @Id
    private String id;

    //上传日期
    @Field(name = "upload_date")
    private String uploadDate;

    //订单文件id
    @Field(name = "file_id")
    private String fileId;

    //工段：SMT PCBA
    @Field(name = "work_section")
    private String workSection;

    //时间周期类型
    @Field(name = "time_period")
    private String timePeriod;

    //状态 false-暂存 true-发布
    @Field(name = "status")
    private Boolean status;

    //余量
    @Field(name = "remains")
    private List<Remain> remains;

    //排班详情
    @Field(name = "detail")
    private List<PlanDocument> detail;

    // 创建时间
    @Field(value = "create_time")
    private Date createTime;

    // 创建人
    @Field(value = "creator")
    private String creator;

    //版本号
    @Field(name = "version")
    private Integer version;

    //线体周期工作天数
    @Field(name = "line_period_work_days")
    private List<LinePeriodWorkDay> linePeriodWorkDays;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUploadDate() {
        return uploadDate;
    }

    public void setUploadDate(String uploadDate) {
        this.uploadDate = uploadDate;
    }

    public String getWorkSection() {
        return workSection;
    }

    public void setWorkSection(String workSection) {
        this.workSection = workSection;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public List<Remain> getRemains() {
        return remains;
    }

    public void setRemains(List<Remain> remains) {
        this.remains = remains;
    }

    public List<PlanDocument> getDetail() {
        return detail;
    }

    public void setDetail(List<PlanDocument> detail) {
        this.detail = detail;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getTimePeriod() {
        return timePeriod;
    }

    public void setTimePeriod(String timePeriod) {
        this.timePeriod = timePeriod;
    }

    public List<LinePeriodWorkDay> getLinePeriodWorkDays() {
        return linePeriodWorkDays;
    }

    public void setLinePeriodWorkDays(List<LinePeriodWorkDay> linePeriodWorkDays) {
        this.linePeriodWorkDays = linePeriodWorkDays;
    }

    public static class Remain {

        //产品号
        @Field(name = "part_number")
        private String partNumber;

        //周期
        @Field(name = "period")
        private String period;

        //平台
        @Field(name = "model")
        private String model;

        //产品名称
        @Field(name = "product")
        private String product;

        //余量
        @Field(name = "remain")
        private Integer remain;

        public String getPartNumber() {
            return partNumber;
        }

        public void setPartNumber(String partNumber) {
            this.partNumber = partNumber;
        }

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }

        public String getProduct() {
            return product;
        }

        public void setProduct(String product) {
            this.product = product;
        }

        public Integer getRemain() {
            return remain;
        }

        public String getPeriod() {
            return period;
        }

        public void setPeriod(String period) {
            this.period = period;
        }

        public void setRemain(Integer remain) {


            this.remain = remain;
        }

    }


}