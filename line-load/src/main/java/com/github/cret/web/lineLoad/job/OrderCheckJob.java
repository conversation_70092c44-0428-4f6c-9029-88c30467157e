package com.github.cret.web.lineLoad.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.github.cret.web.lineLoad.config.WxWorkConfig;
import com.github.cret.web.lineLoad.entity.wxwork.TextCard;
import com.github.cret.web.lineLoad.service.OrderScheduleService;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Resource;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;

/**
 * 每周四检查本周是否已经上传了订单并完成了排产——定时任务
 */
@Component
public class OrderCheckJob extends QuartzJobBean {

    private static final Logger logger = LoggerFactory.getLogger(OrderCheckJob.class);


    @Resource
    private WxWorkConfig wxWorkConfig;
    @Resource
    private OrderScheduleService orderScheduleService;

    @Override
    protected void executeInternal(@Nonnull JobExecutionContext context) {
        Calendar calendar = Calendar.getInstance();
        String today = DateUtil.format(calendar.getTime(), "yyyy-MM-dd");
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        String datBeforeOne = DateUtil.format(calendar.getTime(), "yyyy-MM-dd");
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        String datBeforeTwo = DateUtil.format(calendar.getTime(), "yyyy-MM-dd");
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        String datBeforeThree = DateUtil.format(calendar.getTime(), "yyyy-MM-dd");
        List<String> uploadDates = Arrays.asList(today, datBeforeOne, datBeforeTwo, datBeforeThree);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");
        //回到今天
        calendar.add(Calendar.DAY_OF_MONTH, 3);

        logger.info("开始检查本周是否已上传订单");
        if (!orderScheduleService.checkOrderExistForDate(uploadDates)) {
            logger.info("本周未上传订单");
            //固定推送给张昊经理
//            String toUser = "6398"; todo
            String toUser = "8594";
            String content = "<div class=\"gray\">" + dateFormat.format(calendar.getTime())+"</div> <div class=\"highlight\">本周还未上传订单进行测算，请知悉！</div>";
            TextCard textCard = new TextCard(toUser, null, null, "线体负荷测算系统通知", content, "https://itsm.hongjing-wh.com:18975", wxWorkConfig.getEnableIdTrans(), wxWorkConfig);
            sendWxWorkMsg(JSONUtil.toJsonStr(textCard));
            return;
        }

        logger.info("开始检查本周上传的订单是否已完成排产");
        if (!orderScheduleService.checkOrderPlanExistForDate(uploadDates)) {
            logger.info("本周上传的订单未完成排产");
//            String toUser = "6398"; todo
            String toUser = "8594";
            String content = "<div class=\"gray\">" + dateFormat.format(calendar.getTime())+"</div> <div class=\"highlight\">本周上传的订单未完成排产，请知悉！</div>";
            TextCard textCard = new TextCard(toUser, null, null, "线体负荷测算系统通知", content, "https://itsm.hongjing-wh.com:18975", wxWorkConfig.getEnableIdTrans(), wxWorkConfig);
            sendWxWorkMsg(JSONUtil.toJsonStr(textCard));
        }

    }

    private void sendWxWorkMsg(String msg) {
        try {
            String apiUrl = wxWorkConfig.getSendTextMsgUrl();
            logger.info("发送企业微信消息到：{}", apiUrl);

            String result = HttpUtil.createPost(apiUrl).contentType("application/json")
                    .body(msg)
                    .execute()
                    .body();

            logger.info("发送企业微信消息返回结果：{}", result);
        } catch (Exception e) {
            logger.error("发送企业微信消息失败", e);
            throw e;
        }
    }


}
