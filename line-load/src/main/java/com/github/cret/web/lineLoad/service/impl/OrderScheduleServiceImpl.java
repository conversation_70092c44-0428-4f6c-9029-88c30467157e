package com.github.cret.web.lineLoad.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.github.cret.web.common.domain.FileRef;
import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.lineLoad.document.*;
import com.github.cret.web.lineLoad.entity.vo.CtVo;
import com.github.cret.web.lineLoad.entity.vo.OrderPlanVo;
import com.github.cret.web.lineLoad.entity.vo.OrderVo;
import com.github.cret.web.lineLoad.enums.TimePeriod;
import com.github.cret.web.lineLoad.enums.WorkSection;
import com.github.cret.web.lineLoad.grpc.client.IntelligentScheduleClient;
import com.github.cret.web.lineLoad.repository.OrderPeriodPlanRepository;
import com.github.cret.web.lineLoad.repository.OrderScheduleRepository;
import com.github.cret.web.lineLoad.repository.UploadFileRepository;
import com.github.cret.web.lineLoad.service.CtMaintenanceService;
import com.github.cret.web.lineLoad.service.OrderScheduleService;
import com.github.cret.web.lineLoad.utils.TimeUtils;
import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.util.SecurityUtil;
import com.github.cret.web.system.service.AttachmentService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class OrderScheduleServiceImpl implements OrderScheduleService {

    private static final Logger log = LoggerFactory.getLogger(OrderScheduleServiceImpl.class);

    // <editor-fold desc="Excel Column Indexes">
    // 月度计划表
    private static final int MONTHLY_SHEET_YEAR_ROW = 3;
    private static final int MONTHLY_SHEET_DATA_START_ROW = 4;
    private static final int MONTHLY_PART_NUMBER_COL = 4;
    private static final int MONTHLY_MODEL_COL = 5;
    private static final int MONTHLY_PRODUCT_COL = 6;
    private static final int MONTHLY_PERIOD_DATA_START_COL = 7;
    private static final int MONTHLY_PERIOD_DATA_END_COL = 31;

    // 周计划表
    private static final int WEEKLY_SHEET_PERIOD_ROW = 0;
    private static final int WEEKLY_SHEET_DATA_START_ROW = 1;
    private static final int WEEKLY_PART_NUMBER_COL = 1;
    private static final int WEEKLY_PUBLISH_DATE_COL = 2;
    private static final int WEEKLY_PERIOD_DATA_START_COL = 3;
    // </editor-fold>

    @Resource
    private OrderPeriodPlanRepository orderPeriodPlanRepository;
    @Resource
    private OrderScheduleRepository orderScheduleRepository;
    @Resource
    private IntelligentScheduleClient intelligentScheduleClient;
    @Resource
    private CtMaintenanceService ctMaintenanceService;
    @Resource
    private AttachmentService attachmentService;
    @Resource
    private UploadFileRepository uploadFileRepository;

    private final MongoTemplate mongoTemplate;

    public OrderScheduleServiceImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public Boolean checkOrderExistToday() {
        return orderPeriodPlanRepository
                .countByUploadDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))) > 0;
    }

    @Override
    public Boolean checkOrderExistForDate(List<String> uploadDates) {
        // 判断指定日期的订单计划数据是否存在
        Integer count = orderPeriodPlanRepository.countForUploadDate(uploadDates);
        return count != null && count > 0;
    }

    @Override
    public Boolean checkOrderPlanExistForDate(List<String> uploadDates) {
        // 判断指定日期的订单是否已完成排产
        Integer count = orderScheduleRepository.countForUploadDate(uploadDates);
        return count != null && count > 0;
    }

    @Override
    public void upload(MultipartFile excel) {
        AuthUser user = SecurityUtil.getCurrentUser();
        Objects.requireNonNull(user, "当前用户未登录");

        Date now = new Date();
        String uploadDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        Map<String, List<OrderPeriodPlan>> orderPeriodPlans;
        try {
            // 1. 解析Excel文件
            orderPeriodPlans = parseExcelFile(excel);
        } catch (Exception e) {
            log.error("上传并处理订单计划文件失败", e);
            throw new RuntimeException("上传解析订单计划文件失败", e);
        }
        if (orderPeriodPlans.isEmpty()) {
            log.error("上传的Excel文件为空或不包含有效数据");
            throw new RuntimeException("上传的Excel文件为空或不包含有效数据");
        }

        // 2.校验是否有产品号的CT未维护
        // 2.1 找出所有产品号
//        Set<String> partNumbers = new HashSet<>();
//        orderPeriodPlans.forEach((k, v) -> v.forEach(plan -> partNumbers.add(plan.getPartNumber())));
        // 2.2 找出所有CT 和 tempCt记录
//        List<CtVo> ctVos = ctMaintenanceService.listAllCt();
//        Set<String> ctPartNumbers = ctVos.stream().map(CtVo::getPartNumber).collect(Collectors.toSet());
//        Set<String> missingCtPartNumbers = partNumbers.stream()
//                .filter(partNumber -> !ctPartNumbers.contains(partNumber)).collect(Collectors.toSet());
        // 2.3 校验是否有产品号在ct记录中找不到
//        if (!missingCtPartNumbers.isEmpty()) {
//            throw SysErrEnum.UPLOAD_FILE_CHECK_FAIL.exception("上传的订单中存在未维护CT的产品号：\n" + missingCtPartNumbers);
//        }

        // 3. 保存上传文件记录
        UploadFile uploadFile;
        try {
            uploadFile = saveUploadFile(excel, user, now);
        } catch (Exception e) {
            log.error("保存文件失败", e);
            throw new RuntimeException("保存文件失败", e);
        }

        // 4. 设置通用属性并保存订单计划
        int maxVersion = orderPeriodPlanRepository.findMaxVersionByUploadDate(uploadDate).orElse(0);
        int currentVersion = maxVersion + 1;
        orderPeriodPlans.forEach((k, v) -> v.forEach(plan -> {
            plan.setFileId(uploadFile.getId());
            plan.setFileKey(uploadFile.getFileKey());
            plan.setCreateTime(now);
            plan.setCreator(user.name());
            plan.setVersion(currentVersion);
            plan.setFileName(uploadFile.getFileName());
            plan.setUploadDate(uploadDate);
        }));
        List<OrderPeriodPlan> allPlans = new ArrayList<>();
        allPlans.addAll(orderPeriodPlans.get(TimePeriod.MONTHLY.name()));
        allPlans.addAll(orderPeriodPlans.get(TimePeriod.WEEKLY.name()));
        orderPeriodPlanRepository.saveAll(allPlans);
        log.info("成功解析并保存 {} 条订单计划数据，版本号: {}", orderPeriodPlans.size(), currentVersion);

        // 5. 准备数据分别为月度和周的每个工段调用gRPC服务进行智能排程
        prepareAndCallIntelligentSchedule(orderPeriodPlans.get(TimePeriod.MONTHLY.name()), uploadDate, now, user,
                uploadFile.getId(), TimePeriod.MONTHLY);
        prepareAndCallIntelligentSchedule(orderPeriodPlans.get(TimePeriod.WEEKLY.name()), uploadDate, now, user,
                uploadFile.getId(), TimePeriod.WEEKLY);


    }

    /**
     * 保存上传文件记录
     */
    private UploadFile saveUploadFile(MultipartFile excel, AuthUser user, Date uploadTime) throws IOException {
        FileRef fileRef = attachmentService.upload(excel);
        UploadFile uploadFile = new UploadFile(1, excel.getOriginalFilename(), fileRef.fileKey(), uploadTime,
                user.name());
        return uploadFileRepository.save(uploadFile);
    }

    /**
     * 准备数据并调用gRPC进行智能排程
     */
    private void prepareAndCallIntelligentSchedule(List<OrderPeriodPlan> plans, String uploadDate, Date now,
                                                   AuthUser user, String fileId, TimePeriod timePeriod) {
        // 1. 获取CT维护数据，并按工段对产品号进行分组
        List<CtVo> allCtVos = ctMaintenanceService.listAllCt();
        // smt的ct可能出现一个产品号维护了B、T面的情况，需要把这个产品号在同一个线体的ct进行相加
        Map<String, CtVo> mergedSmtCtMap = new HashMap<>();
        allCtVos.stream()
                .filter(ctVo -> WorkSection.SMT.name().equals(ctVo.getWorkSection()))
                .forEach(ctVo -> {
                    String key = ctVo.getLine() + ":" + ctVo.getPartNumber();
                    if (mergedSmtCtMap.containsKey(key)) {
                        CtVo existing = mergedSmtCtMap.get(key);
                        // 如果已有记录，合并 CT 时间
                        existing.setCt(NumberUtil
                                .roundStr(Double.parseDouble(existing.getCt()) + Double.parseDouble(ctVo.getCt()), 2));
                        existing.setBoard("");
                    } else {
                        // 新建记录
                        mergedSmtCtMap.put(key, ctVo);
                    }
                });
        List<CtVo> smtCtVos = new ArrayList<>(mergedSmtCtMap.values());
        // dip不用考虑合并ct的情况
        List<CtVo> dipCtVos = allCtVos.stream().filter(ctVo -> ctVo.getWorkSection().equals(WorkSection.DIP.name()))
                .toList();
        Map<String, Set<String>> partNumbersByWorkSection = allCtVos.stream()
                .collect(Collectors.groupingBy(
                        CtVo::getWorkSection,
                        Collectors.mapping(CtVo::getPartNumber, Collectors.toSet())));

        Set<String> smtPartNumbers = partNumbersByWorkSection.getOrDefault(WorkSection.SMT.name(),
                Collections.emptySet());
        Set<String> dipPartNumbers = partNumbersByWorkSection.getOrDefault(WorkSection.DIP.name(),
                Collections.emptySet());

        // 2. 将订单计划(OrderPeriodPlan)转换为 gRPC 使用的 OrderVo
        List<OrderVo> allOrderVos = plans.stream()
                .flatMap(plan -> plan.getPeriodNumberList().stream()
                        .map(period -> new OrderVo(plan.getFileId(), period.getPeriod(), plan.getPartNumber(),
                                plan.getProduct(), period.getNumber(), plan.getModel())))
                .toList();

        // 3. 为不同工段筛选出对应的订单，这步会剔除没有维护ct的产品号
        List<OrderVo> smtOrders = allOrderVos.stream()
                .filter(order -> smtPartNumbers.contains(order.getPartNumber()))
                .toList();
        List<OrderVo> dipOrders = allOrderVos.stream()
                .filter(order -> dipPartNumbers.contains(order.getPartNumber()))
                .toList();

        // 4. 如果有未维护ct的产品号，记录在余量中，用于前端页面临时排产
        List<OrderVo> missingCtOrders = allOrderVos.stream()
                .filter(order -> !smtOrders.contains(order) && !dipOrders.contains(order))
                .toList();

        // 5. 分别为各工段调用gRPC服务
        if (!smtOrders.isEmpty()) {
            callGrpc(smtOrders, smtCtVos, WorkSection.SMT.name(), uploadDate, now, user.name(), fileId, timePeriod,
                    missingCtOrders);
            log.info("已为SMT工段调用gRPC服务，处理 {} 条订单。", smtOrders.size());
        }
        if (!dipOrders.isEmpty()) {
            callGrpc(dipOrders, dipCtVos, WorkSection.DIP.name(), uploadDate, now, user.name(), fileId, timePeriod,
                    missingCtOrders);
            log.info("已为DIP工段调用gRPC服务，处理 {} 条订单。", dipOrders.size());
        }
    }

    private void callGrpc(List<OrderVo> orderVos, List<CtVo> ctVos, String workSection, String uploadDate,
                          Date now, String userId, String fileId, TimePeriod timePeriod, List<OrderVo> missingCtOrders) {
        // 剔除其中没有维护CT的产品号去请求gRPC
        List<PlanDocument> planList = intelligentScheduleClient.call(orderVos, ctVos, timePeriod, workSection);
        OrderSchedule orderSchedule = new OrderSchedule();
        orderSchedule.setWorkSection(workSection);
        // 如果有未维护ct的产品号，记录在余量中，用于前端页面临时排产
        if (!missingCtOrders.isEmpty()) {
            List<OrderSchedule.Remain> remains = new ArrayList<>();
            missingCtOrders.forEach(order -> {
                OrderSchedule.Remain remain = new OrderSchedule.Remain();
                remain.setPartNumber(order.getPartNumber());
                remain.setModel(order.getModel());
                remain.setProduct(order.getProductName());
                remain.setRemain(order.getQuantity());
                remain.setPeriod(TimeUtils.parsePeriod(order.getPeriod()));
                remains.add(remain);
            });
            orderSchedule.setRemains(remains);
        }
        orderSchedule.setDetail(planList);
        orderSchedule.setUploadDate(uploadDate);
        orderSchedule.setCreateTime(now);
        orderSchedule.setCreator(userId);
        // 找出 uploadDate 下最大的一条数据，新的版本号+1
        OrderSchedule latestUploadDateOrderSchedule = orderScheduleRepository.findLatestVersionDataByUploadDateAndWorkSectionAndTimePeriod(uploadDate, workSection, timePeriod.name());
        int version = 1;
        if (latestUploadDateOrderSchedule != null) {
            version = latestUploadDateOrderSchedule.getVersion() + 1;
        }
        orderSchedule.setVersion(version);
        orderSchedule.setFileId(fileId);
        orderSchedule.setStatus(false); // 初始状态为暂存
        orderSchedule.setTimePeriod(timePeriod.name());
        orderSchedule.setLinePeriodWorkDays(getLinePeriodWorkDays(uploadDate, workSection, timePeriod));
        orderScheduleRepository.save(orderSchedule);
    }

    private List<LinePeriodWorkDay> getLinePeriodWorkDays(String uploadDate, String workSection,
                                                                         TimePeriod timePeriod) {
        // 找出所有该工段的线体
        List<CtVo> ctVos = ctMaintenanceService.listAllCt().stream()
                .filter(line -> line.getWorkSection().equals(workSection)).toList();
        List<String> lines = ctVos.stream().map(CtVo::getLine).distinct().toList();
        // 找出最新的订单计划
        List<OrderPeriodPlan> orderPeriodPlans = orderPeriodPlanRepository.findLatestVersionByUploadDate(uploadDate)
                .stream().toList();
        // 找出所有的周期
        List<PeriodNumber> periodNumbers = new ArrayList<>();
        // 找出指定周期的订单计划
        orderPeriodPlans.forEach(orderPeriodPlan -> {
            if (orderPeriodPlan.getTimePeriod().equals(timePeriod.name())) {
                periodNumbers.addAll(orderPeriodPlan.getPeriodNumberList());
            }
        });
        Set<String> periods = periodNumbers.stream().map(PeriodNumber::getPeriod).collect(Collectors.toSet());
        List<LinePeriodWorkDay> linePeriodWorkDays = new ArrayList<>();
        lines.forEach(line -> periods.forEach(period -> {
            String workDays = TimeUtils.getWorkDays(period, timePeriod);
            LinePeriodWorkDay linePeriodWorkDay = new LinePeriodWorkDay(line, workDays,
                    TimeUtils.parsePeriod(period));
            linePeriodWorkDays.add(linePeriodWorkDay);
        }));
        return linePeriodWorkDays;
    }

    /**
     * 解析Excel文件
     */
    private Map<String, List<OrderPeriodPlan>> parseExcelFile(MultipartFile excel) {
        Map<String, List<OrderPeriodPlan>> plansByFileId = new HashMap<>();
        try (Workbook workbook = WorkbookFactory.create(excel.getInputStream())) {
            // 解析月度计划订单表
            Sheet monthlySheet = workbook.getSheet("月度计划订单表");
            if (monthlySheet != null) {
                // 可能出现产品号重复的数据，把它们相同period的订单数量相加
                List<OrderPeriodPlan> monthlyPlans = parseMonthlyPlanSheet(monthlySheet);
                Map<String, OrderPeriodPlan> monthlyPlanMap = new HashMap<>();
                monthlyPlans.forEach(plan -> {
                    String key = plan.getPartNumber();
                    if (monthlyPlanMap.containsKey(key)) {
                        plan.setPeriodNumberList(mergeDuplicateOrders(plan.getPeriodNumberList(),
                                monthlyPlanMap.get(key).getPeriodNumberList()));
                        monthlyPlanMap.put(key, plan);
                    } else {
                        monthlyPlanMap.put(key, plan);
                    }
                });
                plansByFileId.put(TimePeriod.MONTHLY.name(), monthlyPlanMap.values().stream().toList());
            } else {
                log.error("未找到 '月度计划订单表' 工作表");
            }

            // 解析周计划订单表
            Sheet weeklySheet = workbook.getSheet("周计划订单表");
            if (weeklySheet != null) {
                List<OrderPeriodPlan> weeklyPlans = parseWeeklyPlanSheet(weeklySheet);
                Map<String, OrderPeriodPlan> weeklyPlanMap = new HashMap<>();
                weeklyPlans.forEach(plan -> {
                    String key = plan.getPartNumber();
                    if (weeklyPlanMap.containsKey(key)) {
                        plan.setPeriodNumberList(mergeDuplicateOrders(plan.getPeriodNumberList(),
                                weeklyPlanMap.get(key).getPeriodNumberList()));
                        weeklyPlanMap.put(key, plan);
                    } else {
                        weeklyPlanMap.put(key, plan);
                    }
                });
                plansByFileId.put(TimePeriod.WEEKLY.name(), weeklyPlanMap.values().stream().toList());
            } else {
                log.error("未找到 '周计划订单表' 工作表");
            }
        } catch (Exception e) {
            log.error("解析Excel文件失败", e);
            throw new RuntimeException("解析Excel文件失败", e);
        }
        return plansByFileId;
    }

    private List<PeriodNumber> mergeDuplicateOrders(List<PeriodNumber> periodNumberList, List<PeriodNumber> tempList) {
        Map<String, Integer> periodNumberMap = new HashMap<>();
        periodNumberList
                .forEach(periodNumber -> periodNumberMap.put(periodNumber.getPeriod(), periodNumber.getNumber()));

        tempList.forEach(periodNumber -> {
            if (periodNumberMap.containsKey(periodNumber.getPeriod())) {
                periodNumber.setNumber(periodNumber.getNumber() + periodNumberMap.get(periodNumber.getPeriod()));
            }
            periodNumberMap.put(periodNumber.getPeriod(), periodNumber.getNumber());
        });
        return periodNumberMap.entrySet().stream()
                .map(entry -> new PeriodNumber(entry.getKey(), entry.getValue()))
                .toList();
    }

    /**
     * 解析月度计划订单表
     */
    private List<OrderPeriodPlan> parseMonthlyPlanSheet(Sheet sheet) {
        List<OrderPeriodPlan> result = new ArrayList<>();
        Row yearRow = sheet.getRow(MONTHLY_SHEET_YEAR_ROW);
        if (yearRow == null) {
            log.error("月度计划订单表中未找到年份信息行 (第{}行)", MONTHLY_SHEET_YEAR_ROW + 1);
            return result;
        }

        for (int i = MONTHLY_SHEET_DATA_START_ROW; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null)
                continue;

            String partNumber = getCellStringValue(row.getCell(MONTHLY_PART_NUMBER_COL));
            if (!StringUtils.hasText(partNumber))
                continue;

            OrderPeriodPlan orderPeriodPlan = new OrderPeriodPlan();
            orderPeriodPlan.setPartNumber(partNumber);
            orderPeriodPlan.setModel(getCellStringValue(row.getCell(MONTHLY_MODEL_COL)));
            orderPeriodPlan.setProduct(getCellStringValue(row.getCell(MONTHLY_PRODUCT_COL)));
            orderPeriodPlan.setTimePeriod(TimePeriod.MONTHLY.name());

            // 处理周期数据
            List<PeriodNumber> periodNumbers = new ArrayList<>();
            dealExcelData(MONTHLY_PERIOD_DATA_START_COL, MONTHLY_PERIOD_DATA_END_COL, row, yearRow, periodNumbers);

            if (!periodNumbers.isEmpty()) {
                orderPeriodPlan.setPeriodNumberList(periodNumbers);
                result.add(orderPeriodPlan);
            }
        }
        return result;
    }

    /**
     * 解析周计划订单表
     */
    private List<OrderPeriodPlan> parseWeeklyPlanSheet(Sheet sheet) {
        List<OrderPeriodPlan> result = new ArrayList<>();
        Row periodRow = sheet.getRow(WEEKLY_SHEET_PERIOD_ROW);
        if (periodRow == null) {
            log.error("周计划订单表中未找到周期信息行 (第{}行)", WEEKLY_SHEET_PERIOD_ROW + 1);
            return result;
        }

        for (int i = WEEKLY_SHEET_DATA_START_ROW; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null)
                continue;

            String partNumber = getCellStringValue(row.getCell(WEEKLY_PART_NUMBER_COL));
            if (!StringUtils.hasText(partNumber))
                continue;

            OrderPeriodPlan orderPeriodPlan = new OrderPeriodPlan();
            orderPeriodPlan.setPartNumber(partNumber);
            orderPeriodPlan.setPublishDate(getCellStringValue(row.getCell(WEEKLY_PUBLISH_DATE_COL)));
            orderPeriodPlan.setTimePeriod(TimePeriod.WEEKLY.name());
            orderPeriodPlan.setProduct("");

            List<PeriodNumber> periodNumbers = new ArrayList<>();

            dealExcelData(WEEKLY_PERIOD_DATA_START_COL, row.getLastCellNum(), row, periodRow, periodNumbers);

            if (!periodNumbers.isEmpty()) {
                orderPeriodPlan.setPeriodNumberList(periodNumbers);
                result.add(orderPeriodPlan);
            }
        }
        return result;
    }

    private void dealExcelData(int begin, int end, Row row, Row periodRow, List<PeriodNumber> periodNumbers) {
        for (int col = begin; col < end; col++) {
            Cell periodCell = periodRow.getCell(col);
            Cell numberCell = row.getCell(col);
            if (periodCell != null && numberCell != null) {
                String period = getCellStringValue(periodCell);
                // 如果不是yyyy/MM/dd格式的日期，则忽略
                if (!StringUtils.hasText(period) || !period.contains("/"))
                    continue;
                Integer number = getCellIntegerValue(numberCell);
                if (StringUtils.hasText(period) && number != null && number > 0) {
                    periodNumbers.add(new PeriodNumber(period, number));
                }
            }
        }
    }

    @Override
    public PageList<OrderPlanVo> page(PageableParam<Object> param) {
        PageList<OrderPlanVo> pageList = new PageList<>();
        pageList.setTotal(0L);

        //找到当前最近日期最大版本号的订单fileId
        OrderPeriodPlan latestActiveFileId = orderPeriodPlanRepository.findLatestActiveFileId();
        if (null == latestActiveFileId) {
            return pageList;
        }
        String fileId = latestActiveFileId.getFileId();

        SortOperation sortOperation1 = Aggregation.sort(
                Sort.Direction.ASC, "upload_date").and(
                Sort.Direction.DESC, "version");

        // Stage 2: Group by upload_date and get the first document (max version)
        GroupOperation groupOperation = Aggregation.group("upload_date")
                .first("$$ROOT").as("latestDocument");

        // Stage 3: Replace root to promote the nested latestDocument to the top level
        ReplaceRootOperation replaceRootOperation = Aggregation.replaceRoot("latestDocument");

        // Stage 4: Sort the grouped results for consistent pagination
        // Again, sorting by upload_date as a String
        SortOperation sortOperation2 = Aggregation.sort(Sort.Direction.DESC, "upload_date");

        // Stage 5: Apply pagination (skip and limit)
        SkipOperation skipOperation = Aggregation.skip(param.getPageRequest().getOffset());
        LimitOperation limitOperation = Aggregation.limit(param.getPageRequest().getPageSize());

        // Construct the aggregation pipeline
        Aggregation aggregation = Aggregation.newAggregation(
                sortOperation1,
                groupOperation,
                replaceRootOperation,
                sortOperation2,
                skipOperation,
                limitOperation);

        // Execute the aggregation
        AggregationResults<OrderPeriodPlan> results = mongoTemplate.aggregate(
                aggregation, "order_period_plan", OrderPeriodPlan.class);

        List<OrderPlanVo> documents = results.getMappedResults().stream()
                .map(orderPeriodPlan -> new OrderPlanVo(orderPeriodPlan.getUploadDate(), orderPeriodPlan.getFileName(),
                        orderPeriodPlan.getFileId(), !orderPeriodPlan.getFileId().equals(fileId), orderPeriodPlan.getCreator(), orderPeriodPlan.getFileKey()))
                .toList();

        if (documents.isEmpty()) {
            return pageList;
        }

        // --- Get total count for PageImpl construction ---
        AggregationResults<Document> results2 = mongoTemplate.aggregate(
                Aggregation.newAggregation(groupOperation,
                        Aggregation.count().as("count")),
                "order_period_plan", Document.class);
        long total = Long.parseLong(Objects.requireNonNull(results2.getUniqueMappedResult()).get("count").toString());
        pageList.setList(documents);
        pageList.setTotal(total);
        return pageList;

        // 按uploadDate分组，然后获取每个分组下的最大版本的一条数据
        // List<OrderPeriodPlan> latestVersionGroup =
        // orderPeriodPlanRepository.findLatestVersionByUploadDate();
        // return latestVersionGroup.stream()
        // .map(orderPeriodPlan -> new OrderPlanVo(orderPeriodPlan.getUploadDate(),
        // orderPeriodPlan.getFileName(),
        // orderPeriodPlan.getFileId()))
        // .toList();
    }

    @Override
    public OrderSchedule getSchedule(String uploadDate, String workSection, String timePeriod) {
        OrderSchedule orderSchedule = orderScheduleRepository.findLatestVersionDataByUploadDateAndWorkSectionAndTimePeriod(uploadDate,
                workSection, timePeriod);
        if (null == orderSchedule.getRemains()) {
            orderSchedule.setRemains(new ArrayList<>());
        }
        return orderSchedule;
    }

    @Override
    public void save(OrderSchedule orderSchedule) {
        AuthUser user = SecurityUtil.getCurrentUser();
        Objects.requireNonNull(user, "当前用户未登录");

        // 找出 uploadDate 下，该工段版本号最大的一条数据
        OrderSchedule latestOrderSchedule = orderScheduleRepository
                .findLatestVersionDataByUploadDateAndWorkSectionAndTimePeriod(orderSchedule.getUploadDate(),
                        orderSchedule.getWorkSection(), orderSchedule.getTimePeriod());

        int version = 1;
        if (latestOrderSchedule != null) {
            if (latestOrderSchedule.getStatus()) {
                // 如果今天已经发布过，则版本号+1
                version = latestOrderSchedule.getVersion() + 1;
            } else {
                // 如果今天已经暂存过，则版本号不变，并删除原暂存的数据
                version = latestOrderSchedule.getVersion();
                orderScheduleRepository.deleteById(latestOrderSchedule.getId());
            }
        }

        orderSchedule.setVersion(version);
        orderSchedule.setCreateTime(new Date());
        orderSchedule.setCreator(user.name());
        orderScheduleRepository.save(orderSchedule);
    }

    @Override
    public List<OrderSchedule> board() {
        List<OrderSchedule> orderSchedules = new ArrayList<>();
        OrderSchedule smtMonthly = orderScheduleRepository.findLatestVersionDataByWorkSectionAndTimePeriod(WorkSection.SMT.name(), TimePeriod.MONTHLY.name());
        if (null != smtMonthly) {
            orderSchedules.add(smtMonthly);
        }
        OrderSchedule dipMonthly = orderScheduleRepository.findLatestVersionDataByWorkSectionAndTimePeriod(WorkSection.DIP.name(), TimePeriod.MONTHLY.name());
        if (null != dipMonthly) {
            orderSchedules.add(dipMonthly);
        }
        OrderSchedule smtWeekly = orderScheduleRepository.findLatestVersionDataByWorkSectionAndTimePeriod(WorkSection.SMT.name(), TimePeriod.WEEKLY.name());
        if (null != smtWeekly) {
            orderSchedules.add(smtWeekly);
        }
        OrderSchedule dipWeekly = orderScheduleRepository.findLatestVersionDataByWorkSectionAndTimePeriod(WorkSection.DIP.name(), TimePeriod.WEEKLY.name());
        if (null != dipWeekly) {
            orderSchedules.add(dipWeekly);
        }
        return orderSchedules;
    }

    /**
     * 安全地获取单元格的字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return new SimpleDateFormat("yyyy/MM/dd").format(cell.getDateCellValue());
                } else {
                    double numericValue = cell.getNumericCellValue();
                    // 避免输出 ".0"
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                // 尝试将公式结果作为字符串获取
                try {
                    return cell.getStringCellValue().trim();
                } catch (Exception e) {
                    // 如果失败，则尝试获取缓存的数值结果
                    try {
                        double numericValue = cell.getNumericCellValue();
                        if (DateUtil.isCellDateFormatted(cell)) {
                            return new SimpleDateFormat("yyyy/MM/dd").format(cell.getDateCellValue());
                        }
                        if (numericValue == (long) numericValue) {
                            return String.valueOf((long) numericValue);
                        } else {
                            return String.valueOf(numericValue);
                        }
                    } catch (Exception ex) {
                        log.error("无法解析公式单元格的值: {}", cell.getAddress(), ex);
                        return "";
                    }
                }
            default:
                return "";
        }
    }

    /**
     * 安全地获取单元格的整数值
     */
    private Integer getCellIntegerValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case NUMERIC:
                return (int) cell.getNumericCellValue();
            case STRING:
                String stringValue = cell.getStringCellValue().trim();
                if (StringUtils.hasText(stringValue)) {
                    try {
                        return Integer.parseInt(stringValue);
                    } catch (NumberFormatException e) {
                        log.warn("无法将字符串 '{}' 解析为整数: {}", stringValue, cell.getAddress());
                        return null;
                    }
                }
                return null;
            case FORMULA:
                try {
                    return (int) cell.getNumericCellValue();
                } catch (Exception e) {
                    // 如果作为数值失败，尝试作为字符串解析
                    try {
                        String formulaStringValue = cell.getStringCellValue().trim();
                        return StringUtils.hasText(formulaStringValue) ? Integer.parseInt(formulaStringValue) : null;
                    } catch (Exception ex) {
                        log.warn("无法将公式结果解析为整数: {}", cell.getAddress(), ex);
                        return null;
                    }
                }
            default:
                return null;
        }
    }

    @Override
    public void exportSchedule(String uploadDate, String workSection, HttpServletResponse response) throws Exception {
        // 查询数据
        OrderSchedule orderSchedule = orderScheduleRepository.findByUploadDateAndWorkSection(uploadDate, workSection);
        if (orderSchedule == null) {
            throw new RuntimeException("未找到排班计划数据");
        }

        // 创建Excel工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("排班计划");

        // 创建样式
        CellStyle headerStyle = createHeaderStyle(workbook);
        CellStyle dataStyle = createDataStyle(workbook);

        // 写入数据
        writeScheduleData(sheet, orderSchedule, headerStyle, dataStyle, workSection);

        // 设置响应头
        String fileName = String.format("排班计划_%s_%s_%s.xlsx",
            orderSchedule.getUploadDate(),
            orderSchedule.getWorkSection(),
            new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()));

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

        // 写入响应流
        workbook.write(response.getOutputStream());
        workbook.close();
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);

        return style;
    }

    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    /**
     * 写入排班数据到Excel
     */
    private void writeScheduleData(Sheet sheet, OrderSchedule orderSchedule, CellStyle headerStyle, CellStyle dataStyle, String workSection) {
        int currentRow = 0;

        // 写入基本信息
        currentRow = writeBasicInfo(sheet, orderSchedule, headerStyle, dataStyle, currentRow);

        // 空行
        currentRow++;

        // 按线体分组处理detail和line_period_work_days
        Map<String, List<PlanDocument>> detailByLine = groupDetailByLine(orderSchedule.getDetail());
        Map<String, List<LinePeriodWorkDay>> workDaysByLine = groupWorkDaysByLine(orderSchedule.getLinePeriodWorkDays());

        // 写入分组数据
        currentRow = writeGroupedData(sheet, detailByLine, workDaysByLine, headerStyle, dataStyle, currentRow, workSection);

        // 空行
        currentRow++;

        // 写入余量信息
        writeRemainData(sheet, orderSchedule.getRemains(), headerStyle, dataStyle, currentRow);

        // 自动调整列宽，考虑表头和内容
        autoSizeColumnsWithHeaders(sheet);
    }

    /**
     * 写入基本信息
     */
    private int writeBasicInfo(Sheet sheet, OrderSchedule orderSchedule, CellStyle headerStyle, CellStyle dataStyle, int startRow) {
        // 基本信息表头
        Row infoHeaderRow = sheet.createRow(startRow++);
        String[] infoHeaders = {"上传日期", "工段", "时间周期", "状态", "创建时间", "创建人", "版本"};
        for (int i = 0; i < infoHeaders.length; i++) {
            Cell cell = infoHeaderRow.createCell(i);
            cell.setCellValue(infoHeaders[i]);
            cell.setCellStyle(headerStyle);
        }

        // 基本信息数据
        Row infoDataRow = sheet.createRow(startRow++);
        infoDataRow.createCell(0).setCellValue(orderSchedule.getUploadDate());
        infoDataRow.createCell(1).setCellValue(orderSchedule.getWorkSection());
        infoDataRow.createCell(2).setCellValue(orderSchedule.getTimePeriod());
        infoDataRow.createCell(3).setCellValue(orderSchedule.getStatus() ? "发布" : "暂存");
        infoDataRow.createCell(4).setCellValue(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(orderSchedule.getCreateTime()));
        infoDataRow.createCell(5).setCellValue(orderSchedule.getCreator());
        infoDataRow.createCell(6).setCellValue(orderSchedule.getVersion().toString());

        // 应用数据样式
        for (int i = 0; i < infoHeaders.length; i++) {
            infoDataRow.getCell(i).setCellStyle(dataStyle);
        }

        return startRow;
    }

    /**
     * 按线体分组detail数据
     */
    private Map<String, List<PlanDocument>> groupDetailByLine(List<PlanDocument> details) {
        if (details == null) return new HashMap<>();
        return details.stream().collect(Collectors.groupingBy(PlanDocument::getLine));
    }

    /**
     * 按线体分组工作日数据
     */
    private Map<String, List<LinePeriodWorkDay>> groupWorkDaysByLine(List<LinePeriodWorkDay> workDays) {
        if (workDays == null) return new HashMap<>();
        return workDays.stream().collect(Collectors.groupingBy(LinePeriodWorkDay::getLine));
    }

    /**
     * 写入分组数据
     */
    private int writeGroupedData(Sheet sheet, Map<String, List<PlanDocument>> detailByLine,
                                Map<String, List<LinePeriodWorkDay>> workDaysByLine,
                                CellStyle headerStyle, CellStyle dataStyle, int startRow, String workSection) {

        // 写入表头
        Row headerRow = sheet.createRow(startRow++);
        String[] headers = {
            "产线", "排产周期", "周期工作日", "平台", "产品号", "产品名称",
            "生产数量", "工作日数量", "UPH", "订单周期"
        };
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 按线体排序并写入数据，同时处理单元格合并
        startRow = writeGroupedDataWithMerge(sheet, detailByLine, workDaysByLine, headers, dataStyle, startRow, workSection);

        return startRow;
    }

    /**
     * 查找指定周期的工作日数据
     */
    private String findWorkDaysForPeriod(List<LinePeriodWorkDay> workDays, String period) {
        return workDays.stream()
            .filter(wd -> period.equals(wd.getPeriod()))
            .map(LinePeriodWorkDay::getPeriodWorkDays)
            .findFirst()
            .orElse("");
    }

    /**
     * 计算工作日数
     */
    private String calculateWorkDays(String costTimeStr, String workSection) {
        if (!StringUtils.hasText(costTimeStr)) {
            return "";
        }

        try {
            double costTimeSeconds = Double.parseDouble(costTimeStr);
            if (costTimeSeconds <= 0) {
                return "";
            }

            // 根据工段确定每天工作小时数
            double hoursPerDay = "SMT".equals(workSection) ? 22.5 : 23.5;

            // 转换为小时，再计算工作日
            double costTimeHours = costTimeSeconds / 3600.0;
            double workDays = costTimeHours / hoursPerDay;

            return String.format("%.2f", workDays);
        } catch (NumberFormatException e) {
            return "";
        }
    }

    /**
     * 计算UPH (每小时产出)
     */
    private String calculateUPH(Integer quantity, String costTimeStr) {
        if (quantity == null || !StringUtils.hasText(costTimeStr)) {
            return "";
        }

        try {
            double costTimeSeconds = Double.parseDouble(costTimeStr);
            if (costTimeSeconds <= 0) {
                return "";
            }

            // 转换为小时
            double costTimeHours = costTimeSeconds / 3600.0;
            double uph = quantity / costTimeHours;

            return String.format("%.2f", uph);
        } catch (NumberFormatException e) {
            return "";
        }
    }

    /**
     * 写入余量数据
     */
    private void writeRemainData(Sheet sheet, List<OrderSchedule.Remain> remains,
                                CellStyle headerStyle, CellStyle dataStyle, int startRow) {
        if (remains == null || remains.isEmpty()) {
            return;
        }

        // 余量表头
        Row remainHeaderRow = sheet.createRow(startRow++);
        String[] remainHeaders = {"订单周期", "平台", "产品号", "产品名称", "余量"};
        for (int i = 0; i < remainHeaders.length; i++) {
            Cell cell = remainHeaderRow.createCell(i);
            cell.setCellValue(remainHeaders[i]);
            cell.setCellStyle(headerStyle);
        }

        // 余量数据
        for (OrderSchedule.Remain remain : remains) {
            Row remainDataRow = sheet.createRow(startRow++);
            remainDataRow.createCell(0).setCellValue(remain.getPeriod());
            remainDataRow.createCell(1).setCellValue(remain.getModel());
            remainDataRow.createCell(2).setCellValue(remain.getPartNumber());
            remainDataRow.createCell(3).setCellValue(remain.getProduct());
            remainDataRow.createCell(4).setCellValue(remain.getRemain());

            // 应用数据样式
            for (int i = 0; i < remainHeaders.length; i++) {
                if (remainDataRow.getCell(i) != null) {
                    remainDataRow.getCell(i).setCellStyle(dataStyle);
                }
            }
        }
    }

    /**
     * 写入分组数据并处理单元格合并
     */
    private int writeGroupedDataWithMerge(Sheet sheet, Map<String, List<PlanDocument>> detailByLine,
                                         Map<String, List<LinePeriodWorkDay>> workDaysByLine,
                                         String[] headers, CellStyle dataStyle, int startRow, String workSection) {

        // 按线体排序
        Set<String> allLines = new TreeSet<>();
        allLines.addAll(detailByLine.keySet());
        allLines.addAll(workDaysByLine.keySet());

        for (String line : allLines) {
            List<PlanDocument> lineDetails = detailByLine.getOrDefault(line, new ArrayList<>());
            List<LinePeriodWorkDay> lineWorkDays = workDaysByLine.getOrDefault(line, new ArrayList<>());

            if (!lineDetails.isEmpty()) {
                // 按排产周期分组
                Map<String, List<PlanDocument>> detailsByPeriod = lineDetails.stream()
                    .collect(Collectors.groupingBy(PlanDocument::getProductionPeriod, LinkedHashMap::new, Collectors.toList()));

                int lineStartRow = startRow;
                int lineRowCount = 0;

                for (Map.Entry<String, List<PlanDocument>> periodEntry : detailsByPeriod.entrySet()) {
                    String period = periodEntry.getKey();
                    List<PlanDocument> periodDetails = periodEntry.getValue();

                    int periodStartRow = startRow;

                    for (int i = 0; i < periodDetails.size(); i++) {
                        PlanDocument detail = periodDetails.get(i);
                        Row dataRow = sheet.createRow(startRow++);
                        lineRowCount++;

                        // 产线列（只在第一行填写，后续合并）
                        if (startRow - 1 == lineStartRow) {
                            dataRow.createCell(0).setCellValue(detail.getLine());
                        } else {
                            dataRow.createCell(0).setCellValue("");
                        }

                        // 排产周期列（只在该周期第一行填写，后续合并）
                        if (i == 0) {
                            dataRow.createCell(1).setCellValue(detail.getProductionPeriod());
                        } else {
                            dataRow.createCell(1).setCellValue("");
                        }

                        // 查找对应的工作日数据
                        String workDays = findWorkDaysForPeriod(lineWorkDays, detail.getProductionPeriod());
                        dataRow.createCell(2).setCellValue(workDays);

                        dataRow.createCell(3).setCellValue(detail.getModel());
                        dataRow.createCell(4).setCellValue(detail.getPartNumber());
                        dataRow.createCell(5).setCellValue(detail.getProductName());
                        dataRow.createCell(6).setCellValue(detail.getQuantity());

                        // 计算工作日
                        String workDaysCalculated = calculateWorkDays(detail.getCostTime(), workSection);
                        dataRow.createCell(7).setCellValue(workDaysCalculated);

                        // 计算UPH
                        String uph = calculateUPH(detail.getQuantity(), detail.getCostTime());
                        dataRow.createCell(8).setCellValue(uph);

                        dataRow.createCell(9).setCellValue(detail.getOrderPeriod());

                        // 应用数据样式
                        for (int j = 0; j < headers.length; j++) {
                            if (dataRow.getCell(j) != null) {
                                dataRow.getCell(j).setCellStyle(dataStyle);
                            }
                        }
                    }

                    // 合并排产周期单元格
                    if (periodDetails.size() > 1) {
                        sheet.addMergedRegion(new CellRangeAddress(periodStartRow, startRow - 1, 1, 1));
                    }
                }

                // 合并产线单元格
                if (lineRowCount > 1) {
                    sheet.addMergedRegion(new CellRangeAddress(lineStartRow, startRow - 1, 0, 0));
                }

            } else if (!lineWorkDays.isEmpty()) {
                // 如果只有工作日数据，没有详细数据
                for (LinePeriodWorkDay workDay : lineWorkDays) {
                    Row dataRow = sheet.createRow(startRow++);
                    dataRow.createCell(0).setCellValue(workDay.getLine());
                    dataRow.createCell(1).setCellValue(workDay.getPeriod());
                    dataRow.createCell(2).setCellValue(workDay.getPeriodWorkDays());

                    // 应用数据样式
                    for (int i = 0; i < 3; i++) {
                        if (dataRow.getCell(i) != null) {
                            dataRow.getCell(i).setCellStyle(dataStyle);
                        }
                    }
                }
            }
        }

        return startRow;
    }

    /**
     * 自动调整列宽，考虑表头和内容
     */
    private void autoSizeColumnsWithHeaders(Sheet sheet) {
        // 先自动调整列宽
        for (int i = 0; i < 15; i++) {
            sheet.autoSizeColumn(i);
        }

        // 检查表头宽度并调整
        for (int i = 0; i < 15; i++) {
            // 获取当前列宽
            int currentWidth = sheet.getColumnWidth(i);

            // 检查所有行的该列，找到最长的内容
            int maxWidth = currentWidth;

            for (Row row : sheet) {
                Cell cell = row.getCell(i);
                if (cell != null) {
                    String cellValue = getCellDisplayValue(cell);
                    if (StringUtils.hasText(cellValue)) {
                        // 估算文字宽度（中文字符按2个字符计算）
                        int estimatedWidth = estimateStringWidth(cellValue) * 256;
                        maxWidth = Math.max(maxWidth, estimatedWidth);
                    }
                }
            }

            // 设置最小宽度和最大宽度限制
            maxWidth = Math.max(maxWidth, 2000); // 最小宽度
            maxWidth = Math.min(maxWidth, 15000); // 最大宽度

            sheet.setColumnWidth(i, maxWidth);
        }
    }

    /**
     * 获取单元格显示值
     */
    private String getCellDisplayValue(Cell cell) {
        if (cell == null) return "";

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            default:
                return "";
        }
    }

    /**
     * 估算字符串宽度（中文字符按2个字符计算）
     */
    private int estimateStringWidth(String str) {
        if (str == null) return 0;

        int width = 0;
        for (char c : str.toCharArray()) {
            // 中文字符范围
            if (c >= 0x4E00 && c <= 0x9FFF) {
                width += 2;
            } else {
                width += 1;
            }
        }
        return width;
    }
}
