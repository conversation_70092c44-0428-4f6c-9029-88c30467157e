package com.github.cret.web.lineLoad.grpc.client;

import com.github.cret.web.lineLoad.document.PlanDocument;
import com.github.cret.web.lineLoad.entity.vo.CtVo;
import com.github.cret.web.lineLoad.entity.vo.OrderVo;
import com.github.cret.web.lineLoad.enums.TimePeriod;
import com.github.cret.web.lineLoad.enums.WorkSection;
import com.github.cret.web.lineLoad.grpc.generate.*;
import com.github.cret.web.lineLoad.utils.TimeUtils;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import jakarta.annotation.PostConstruct;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
public class IntelligentScheduleClient {


    @Value("${grpc.client.line-load.address}")
    private String address;
    @Value("${grpc.client.line-load.port}")
    private int port;

    private LpServiceGrpc.LpServiceBlockingStub lpServiceBlockingStub;

    @PostConstruct
    private void init() {
        ManagedChannel managedChannel = ManagedChannelBuilder
                .forAddress(address, port).usePlaintext().build();
        lpServiceBlockingStub = LpServiceGrpc.newBlockingStub(managedChannel);
    }

    public List<PlanDocument> call(List<OrderVo> orderVos, List<CtVo> ctVos, TimePeriod timePeriod, String workSection) {

        double workHour = workSection.equals(WorkSection.SMT.name()) ? 23.5 : 22.5;

        List<Order> orders = new ArrayList<>();
        orderVos.forEach(orderVo -> {
            Order order = Order.newBuilder().setId(orderVo.getFileId())
                    .setPeriod(TimeUtils.parsePeriod(orderVo.getPeriod()))
                    .setPartNumber(orderVo.getPartNumber())
                    .setProductName(orderVo.getProductName())
                    .setQuantity(orderVo.getQuantity())
                    .setModel(null == orderVo.getModel() ? "" : orderVo.getModel())
                    .build();
            orders.add(order);
        });

        List<Ct> cts = new ArrayList<>();
        ctVos.forEach(ctVo -> {
            Ct ct = Ct.newBuilder()
                    .setPartNumber(ctVo.getPartNumber())
                    .setLine(ctVo.getLine())
                    .setCt(ctVo.getCt())
                    .build();
            cts.add(ct);
        });

        List<ProductionTime> productionTimes = new ArrayList<>();
        //根据周期类型和周期，获得该周期的工作日天数
        List<String> lines = cts.stream().map(Ct::getLine).distinct().toList();
        List<String> periods = orderVos.stream().map(OrderVo::getPeriod).distinct().toList();
        lines.forEach(line -> periods.forEach(period -> {
            String workDays = TimeUtils.getWorkDays(period, timePeriod);
            ProductionTime productionTime = ProductionTime.newBuilder()
                    .setLine(line)
                    .setPeriod(TimeUtils.parsePeriod(period))
                    .setTime((int) (Integer.parseInt(workDays) * workHour * 3600))
                    .build();
            productionTimes.add(productionTime);
        }));

        LpRequest request = LpRequest.newBuilder()
                .addAllOrders(orders)
                .addAllCts(cts)
                .addAllProductionTimes(productionTimes)
                .build();

        LpResponse response = lpServiceBlockingStub.lp(request);

        return getPlanVos(response);
    }

    @NotNull
    private static List<PlanDocument> getPlanVos(LpResponse response) {
        List<Plan> plans = response.getPlansList();
        List<PlanDocument> planDocuments = new ArrayList<>();
        String uploadDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        for (Plan plan : plans) {
            PlanDocument planDocument = new PlanDocument();
            planDocument.setPartNumber(plan.getPartNumber());
            planDocument.setModel(plan.getModel());
            planDocument.setProductName(plan.getProductName());
            planDocument.setLine(plan.getLine());
            planDocument.setOrderPeriod(plan.getOrderPeriod());
            planDocument.setProductionPeriod(plan.getProductionPeriod());
            planDocument.setQuantity(plan.getQuantity());
            planDocument.setCostTime(plan.getCostTime());
            planDocument.setUploadDate(uploadDate);
            planDocuments.add(planDocument);
        }
        return planDocuments;
    }




}
