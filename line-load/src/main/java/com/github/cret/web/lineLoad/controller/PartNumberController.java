package com.github.cret.web.lineLoad.controller;

import com.github.cret.web.lineLoad.service.PartNumberService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/partNumber")
public class PartNumberController {

    @Resource
    private PartNumberService partNumberService;

    @RequestMapping("/list")
    public List<String> list() {
        return partNumberService.list();
    }

}
