package com.github.cret.web.lineLoad.entity.wxwork;

import com.github.cret.web.lineLoad.config.WxWorkConfig;

public class Text extends BaseReq{

    TextContent text;
    Integer safe;
    Integer enable_id_trans;

    public TextContent getText() {
        return text;
    }

    public void setText(TextContent text) {
        this.text = text;
    }

    public Integer getSafe() {
        return safe;
    }

    public void setSafe(Integer safe) {
        this.safe = safe;
    }

    public Integer getEnable_id_trans() {
        return enable_id_trans;
    }

    public void setEnable_id_trans(Integer enable_id_trans) {
        this.enable_id_trans = enable_id_trans;
    }

    public static class TextContent {
        private String content;

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }

    public Text(String toUser, String toParty, String toTag, String content, int enable_id_trans, WxWorkConfig wxWorkConfig) {
        super(toUser, toParty, toTag, "text", wxWorkConfig);
        this.text = new Text.TextContent();
        this.text.content = content;
        this.enable_id_trans = enable_id_trans;
    }
}
