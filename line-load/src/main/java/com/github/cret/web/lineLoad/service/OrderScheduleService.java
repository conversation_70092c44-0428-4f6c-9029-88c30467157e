package com.github.cret.web.lineLoad.service;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.lineLoad.document.OrderSchedule;
import com.github.cret.web.lineLoad.entity.vo.OrderPlanVo;

public interface OrderScheduleService {

    Boolean checkOrderExistToday();

    Boolean checkOrderExistForDate(List<String> uploadDate);

    Boolean checkOrderPlanExistForDate(List<String> uploadDate);

    void upload(MultipartFile excel);

    PageList<OrderPlanVo> page(PageableParam<Object> pageableParam);

    OrderSchedule getSchedule(String uploadDate, String workSection, String timePeriod);

    void save(OrderSchedule orderSchedule);

    List<OrderSchedule> board();

    /**
     * 导出排班计划Excel
     */
    void exportSchedule(String uploadDate, String workSection, HttpServletResponse response) throws Exception;
}
