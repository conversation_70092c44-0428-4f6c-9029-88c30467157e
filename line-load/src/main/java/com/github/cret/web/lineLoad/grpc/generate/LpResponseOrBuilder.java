// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: IntelligentSchedule.proto

// Protobuf Java Version: 3.25.1
package com.github.cret.web.lineLoad.grpc.generate;

public interface LpResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.hongjingwh.lp.LpResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated .com.hongjingwh.lp.Plan plans = 1;</code>
   */
  java.util.List<com.github.cret.web.lineLoad.grpc.generate.Plan> 
      getPlansList();
  /**
   * <code>repeated .com.hongjingwh.lp.Plan plans = 1;</code>
   */
  com.github.cret.web.lineLoad.grpc.generate.Plan getPlans(int index);
  /**
   * <code>repeated .com.hongjingwh.lp.Plan plans = 1;</code>
   */
  int getPlansCount();
  /**
   * <code>repeated .com.hongjingwh.lp.Plan plans = 1;</code>
   */
  java.util.List<? extends com.github.cret.web.lineLoad.grpc.generate.PlanOrBuilder> 
      getPlansOrBuilderList();
  /**
   * <code>repeated .com.hongjingwh.lp.Plan plans = 1;</code>
   */
  com.github.cret.web.lineLoad.grpc.generate.PlanOrBuilder getPlansOrBuilder(
      int index);
}
