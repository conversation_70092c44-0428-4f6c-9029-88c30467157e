package com.github.cret.web.lineLoad.service.impl;

import com.github.cret.web.lineLoad.document.CtMaintenance;
import com.github.cret.web.lineLoad.document.OrderPeriodPlan;
import com.github.cret.web.lineLoad.document.OrderSchedule;
import com.github.cret.web.lineLoad.entity.vo.req.LineLoadReq;
import com.github.cret.web.lineLoad.entity.vo.res.CtRes;
import com.github.cret.web.lineLoad.entity.vo.res.LineLoadRes;
import com.github.cret.web.lineLoad.entity.vo.res.OrderRes;
import com.github.cret.web.lineLoad.enums.TimePeriod;
import com.github.cret.web.lineLoad.enums.WorkSection;
import com.github.cret.web.lineLoad.repository.CtMaintenanceRepository;
import com.github.cret.web.lineLoad.service.AnalysisService;
import com.google.common.util.concurrent.AtomicDouble;
import jakarta.annotation.Resource;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
public class AnalysisServiceImpl implements AnalysisService {

    private final MongoTemplate mongoTemplate;

    public AnalysisServiceImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Resource
    private CtMaintenanceRepository ctMaintenanceRepository;

    @Override
    public List<LineLoadRes> getLoadByLineAndMonth(LineLoadReq lineLoadReq) {
        List<AggregationOperation> operations = new ArrayList<>();
        operations.add(Aggregation.match(Criteria.where("status").is(true)));
        operations.add(Aggregation.match(Criteria.where("time_period").is(TimePeriod.MONTHLY.name())));

        if (StringUtils.hasText(lineLoadReq.getStartUploadDate())) {
            operations.add(Aggregation.match(Criteria.where("upload_date").gte(lineLoadReq.getStartUploadDate())));
        }
        if (StringUtils.hasText(lineLoadReq.getEndUploadDate())) {
            operations.add(Aggregation.match(Criteria.where("upload_date").lte(lineLoadReq.getEndUploadDate())));
        }

        operations.add(Aggregation.unwind("detail"));

        if (StringUtils.hasText(lineLoadReq.getLine())) {
            operations.add(Aggregation.match(Criteria.where("detail.line").is(lineLoadReq.getLine())));
        }
        if (StringUtils.hasText(lineLoadReq.getStartMonth())) {
            operations.add(Aggregation.match(Criteria.where("detail.order_period").gte(lineLoadReq.getStartMonth())));
        }
        if (StringUtils.hasText(lineLoadReq.getEndMonth())) {
            operations.add(Aggregation.match(Criteria.where("detail.order_period").lte(lineLoadReq.getEndMonth())));
        }

        operations.add(Aggregation.group("_id")
                .first("upload_date").as("upload_date")
                .first("file_id").as("file_id")
                .first("work_section").as("work_section")
                .first("time_period").as("time_period")
                .first("version").as("version")
                .first("line_period_work_days").as("line_period_work_days")
                .addToSet("detail").as("detail")
        );

        operations.add(Aggregation.unwind("line_period_work_days"));

        if (StringUtils.hasText(lineLoadReq.getLine())) {
            operations.add(Aggregation.match(Criteria.where("line_period_work_days.line").is(lineLoadReq.getLine())));
        }

        operations.add(Aggregation.group("_id")
                .first("upload_date").as("upload_date")
                .first("file_id").as("file_id")
                .first("work_section").as("work_section")
                .first("time_period").as("time_period")
                .first("version").as("version")
                .first("detail").as("detail")
                .addToSet("line_period_work_days").as("line_period_work_days")
        );

        Aggregation aggregation = Aggregation.newAggregation(operations);
        AggregationResults<OrderSchedule> results = mongoTemplate.aggregate(aggregation, "order_schedule", OrderSchedule.class);
        List<OrderSchedule> mappedResults = results.getMappedResults();

        //按uploadDate分组，然后去掉同一个uploadDate中，version中不是最大的所有数据
        mappedResults = mappedResults.stream()
                .collect(Collectors.groupingBy(OrderSchedule::getUploadDate))
                .values().stream()
                .map(list -> list.stream().max(Comparator.comparingInt(OrderSchedule::getVersion)).orElse(null))
                .toList();

        Map<String, List<OrderSchedule>> groupedByUploadDate = mappedResults.stream()
                .collect(Collectors.groupingBy(OrderSchedule::getUploadDate));

        List<LineLoadRes> lineLoadResList = new ArrayList<>();
        for (Map.Entry<String, List<OrderSchedule>> entry : groupedByUploadDate.entrySet()) {
            String uploadDate = entry.getKey();
            List<OrderSchedule> schedules = entry.getValue();

            double totalCostTime = schedules.stream()
                    .flatMap(s -> s.getDetail().stream())
                    .mapToDouble(d -> Double.parseDouble(d.getCostTime()))
                    .sum();

            AtomicDouble workTime = new AtomicDouble();
            schedules.stream().filter(schedule -> schedule.getWorkSection().equals(WorkSection.SMT.name())).forEach(schedule -> {
                int availableDays = schedules.stream()
                        .flatMap(s -> s.getLinePeriodWorkDays().stream())
                        .filter(l -> schedules.stream().anyMatch(s -> s.getDetail().stream().anyMatch(d -> d.getProductionPeriod().equals(l.getPeriod()))))
                        .mapToInt(l -> Integer.parseInt(l.getPeriodWorkDays()))
                        .sum();
                workTime.addAndGet((long) (availableDays * 23.5 * 3600));
            });
            schedules.stream().filter(schedule -> schedule.getWorkSection().equals(WorkSection.DIP.name())).forEach(schedule -> {
                int availableDays = schedules.stream()
                        .flatMap(s -> s.getLinePeriodWorkDays().stream())
                        .filter(l -> schedules.stream().anyMatch(s -> s.getDetail().stream().anyMatch(d -> d.getProductionPeriod().equals(l.getPeriod()))))
                        .mapToInt(l -> Integer.parseInt(l.getPeriodWorkDays()))
                        .sum();
                workTime.addAndGet((long) (availableDays * 22.5 * 3600));
            });

            LineLoadRes lineLoadRes = new LineLoadRes();
            lineLoadRes.setUploadDate(uploadDate);
            lineLoadRes.setCostTime(String.valueOf(totalCostTime));
            lineLoadRes.setAvailableTime(String.valueOf(workTime.doubleValue()));
            lineLoadResList.add(lineLoadRes);
        }

        return lineLoadResList.stream().sorted(Comparator.comparing(LineLoadRes::getUploadDate)).toList();
    }

    @Override
    public List<CtRes> analysisCt(String line, String partNum, Date startCreateTime, Date endCreateTime) {
        if (null == startCreateTime) {
            startCreateTime = new Date(0);
        }
        if (null == endCreateTime) {
            endCreateTime = new Date();
        }
        List<CtMaintenance> ctMaintenances = ctMaintenanceRepository.findByLineAndPartNumberAndCreateTimeBetween(line, partNum, startCreateTime, endCreateTime);

        //根据create_time分组，把同一组中的procedure中相同name的ct相加，再把maxCt相加，最后再汇总成List<CtMaintenance>
        List<CtMaintenance> finalCtMaintenances = new ArrayList<>();
        Map<Date, List<CtMaintenance>> groupedByCreateTime = ctMaintenances.stream()
                .collect(Collectors.groupingBy(CtMaintenance::getCreateTime));
        groupedByCreateTime.forEach((createTime, ctMaintenancesList) -> {
            CtMaintenance ctMaintenance = new CtMaintenance();
            ctMaintenance.setCreateTime(createTime);
            ctMaintenance.setPartNumber(partNum);
            Map<String, Double> procedureCtMap = new HashMap<>();
            double maxCt = 0.0;
            for (CtMaintenance ct : ctMaintenancesList) {
                maxCt += Double.parseDouble(ct.getMaxCt());
                ct.getProcedure().forEach(procedure -> procedureCtMap.merge(procedure.getName(), procedure.getCt(), Double::sum));
            }
            ctMaintenance.setMaxCt(String.valueOf(maxCt));
            ctMaintenance.setProcedure(procedureCtMap.entrySet().stream().map(entry -> {
                CtMaintenance.Procedure procedure = new CtMaintenance.Procedure();
                procedure.setName(entry.getKey());
                procedure.setCt(entry.getValue());
                return procedure;
            }).toList());
            finalCtMaintenances.add(ctMaintenance);
        });

        List<CtRes> ctResList = new ArrayList<>();
        finalCtMaintenances.forEach(ctMaintenance -> {
            CtRes ctRes = new CtRes();
            ctRes.setCt(ctMaintenance.getMaxCt());
            ctRes.setCreateTime(ctMaintenance.getCreateTime());
            ctRes.setWorkOrderCts(ctMaintenance.getProcedure().stream().map(procedure -> {
                CtRes.WorkOrderCt workOrderCt = new CtRes.WorkOrderCt();
                workOrderCt.setName(procedure.getName());
                workOrderCt.setCt(String.valueOf(procedure.getCt()));
                return workOrderCt;
            }).toList());
            ctResList.add(ctRes);
        });

        ctResList.sort(Comparator.comparing(CtRes::getCreateTime));
        return ctResList;
    }

    @Override
    public List<OrderRes> analysisOrder(String partNum, String periodType, String beginOrderDate, String endOrderDate, String beginUploadDate, String endUploadDate) {
        List<AggregationOperation> operations = new ArrayList<>();

        if(StringUtils.hasText(partNum)){
            operations.add(Aggregation.match(Criteria.where("part_number").is(partNum)));
        }
        operations.add(Aggregation.match(Criteria.where("time_period").is(periodType)));

        if (StringUtils.hasText(beginUploadDate)) {
            operations.add(Aggregation.match(Criteria.where("upload_date").gte(beginUploadDate)));
        }
        if (StringUtils.hasText(endUploadDate)) {
            operations.add(Aggregation.match(Criteria.where("upload_date").lte(endUploadDate)));
        }

        operations.add(Aggregation.unwind("period_number"));
        if (StringUtils.hasText(beginOrderDate)) {
            operations.add(Aggregation.match(Criteria.where("period_number.period").gte(beginOrderDate)));
        }
        if (StringUtils.hasText(endOrderDate)) {
            operations.add(Aggregation.match(Criteria.where("period_number.period").lte(endOrderDate)));
        }

        operations.add(Aggregation.group("_id")
                .first("upload_date").as("upload_date")
                .first("part_number").as("part_number")
                .first("model").as("model")
                .first("time_period").as("time_period")
                .first("file_id").as("file_id")
                .first("product").as("product")
                .first("version").as("version")
                .addToSet("period_number").as("period_number")
        );

        Aggregation aggregation = Aggregation.newAggregation(operations);
        AggregationResults<OrderPeriodPlan> results = mongoTemplate.aggregate(aggregation, "order_period_plan", OrderPeriodPlan.class);
        List<OrderPeriodPlan> mappedResults = results.getMappedResults();

        //按uploadDate分组，然后去掉同一个uploadDate中，version中不是最大的所有数据
        mappedResults = mappedResults.stream()
                .collect(Collectors.groupingBy(OrderPeriodPlan::getUploadDate))
                .values().stream()
                .map(list -> list.stream().max(Comparator.comparingInt(OrderPeriodPlan::getVersion)).orElse(null))
                .toList();

        Map<String, List<OrderPeriodPlan>> groupedByUploadDate = mappedResults.stream()
                .collect(Collectors.groupingBy(OrderPeriodPlan::getUploadDate));

        List<OrderRes> orderResList = new ArrayList<>();
        for (Map.Entry<String, List<OrderPeriodPlan>> entry : groupedByUploadDate.entrySet()) {
            String uploadDate = entry.getKey();
            List<OrderPeriodPlan> periodPlans = entry.getValue();
            AtomicInteger number = new AtomicInteger();
            periodPlans.forEach(plan -> plan.getPeriodNumberList().forEach(periodNumber -> number.addAndGet(periodNumber.getNumber())));
            orderResList.add(new OrderRes(uploadDate, number.get()));
        }

        orderResList.sort(Comparator.comparing(OrderRes::getUploadDate));
        return orderResList;
    }

}
