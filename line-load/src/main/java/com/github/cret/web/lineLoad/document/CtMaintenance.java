package com.github.cret.web.lineLoad.document;


import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;
import java.util.List;

/**
 * Description: 线体-产品号-工序维护
 * date: 2025/7/9 10:19
 *
 * @author: zxk
 */
@Document("ct_maintenance")
public class CtMaintenance {

	@Id
	private String id;

	//线体
	@Field(name = "line")
	private String line;

	//产品号
	@Field(name = "part_number")
	private String partNumber;

	//平台
	@Field(name = "model")
	private String model;

	//工段：SMT DIP
	@Field(name = "work_section")
	private String workSection;

	//版面
	@Field(name = "board")
	private String board;

	//工序
	@Field(name = "procedure")
	private List<Procedure> procedure;

	//最大ct
	@Field(name = "max_ct")
	private String maxCt;

	// 创建时间
	@Field(value = "create_time")
	private Date createTime;

	// 创建人
	@Field(value = "creator")
	private String creator;

	//版本号
	@Field(name = "version")
	private Integer version;

	//文件id
	@Field(name = "file_id")
	private String fileId;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getLine() {
		return line;
	}

	public void setLine(String line) {
		this.line = line;
	}

	public String getPartNumber() {
		return partNumber;
	}

	public void setPartNumber(String partNumber) {
		this.partNumber = partNumber;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getWorkSection() {
		return workSection;
	}

	public void setWorkSection(String workSection) {
		this.workSection = workSection;
	}

	public List<Procedure> getProcedure() {
		return procedure;
	}

	public void setProcedure(List<Procedure> procedure) {
		this.procedure = procedure;
	}

	public String getMaxCt() {
		return maxCt;
	}

	public void setMaxCt(String maxCt) {
		this.maxCt = maxCt;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Integer getVersion() {
		return version;
	}

	public void setVersion(Integer version) {
		this.version = version;
	}

	public String getBoard() {
		return board;
	}

	public void setBoard(String board) {
		this.board = board;
	}

	public String getFileId() {
		return fileId;
	}

	public void setFileId(String fileId) {
		this.fileId = fileId;
	}

	/**
	 * Description: 线体-产品号-工序-CT维护
	 * date: 2025/7/9 10:20
	 *
	 * @author: zxk
	 */
    public static class Procedure {

		//名称
		@Field(name = "name")
		private String name;

		//ct
		@Field(name = "ct")
		private Double ct;

		public Procedure() {
		}

		public Procedure(String name, Double ct) {
			this.name = name;
			this.ct = ct;
		}

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}

		public Double getCt() {
			return ct;
		}

		public void setCt(Double ct) {
			this.ct = ct;
		}

	}
}