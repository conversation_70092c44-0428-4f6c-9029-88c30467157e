// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: IntelligentSchedule.proto

// Protobuf Java Version: 3.25.1
package com.github.cret.web.lineLoad.grpc.generate;

public interface ProductionTimeOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.hongjingwh.lp.ProductionTime)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *产线编号
   * </pre>
   *
   * <code>string line = 1;</code>
   * @return The line.
   */
  java.lang.String getLine();
  /**
   * <pre>
   *产线编号
   * </pre>
   *
   * <code>string line = 1;</code>
   * @return The bytes for line.
   */
  com.google.protobuf.ByteString
      getLineBytes();

  /**
   * <pre>
   *生产周期 （年月或周） eg. 2025-01
   * </pre>
   *
   * <code>string period = 2;</code>
   * @return The period.
   */
  java.lang.String getPeriod();
  /**
   * <pre>
   *生产周期 （年月或周） eg. 2025-01
   * </pre>
   *
   * <code>string period = 2;</code>
   * @return The bytes for period.
   */
  com.google.protobuf.ByteString
      getPeriodBytes();

  /**
   * <pre>
   *可用生产时间
   * </pre>
   *
   * <code>int32 time = 3;</code>
   * @return The time.
   */
  int getTime();
}
