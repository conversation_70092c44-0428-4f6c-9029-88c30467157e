// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: IntelligentSchedule.proto

// Protobuf Java Version: 3.25.1
package com.github.cret.web.lineLoad.grpc.generate;

public interface OrderOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.hongjingwh.lp.Order)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *订单ID
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   *订单ID
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <pre>
   *订单周期（年月或周） eg.  2025-01
   * </pre>
   *
   * <code>string period = 2;</code>
   * @return The period.
   */
  java.lang.String getPeriod();
  /**
   * <pre>
   *订单周期（年月或周） eg.  2025-01
   * </pre>
   *
   * <code>string period = 2;</code>
   * @return The bytes for period.
   */
  com.google.protobuf.ByteString
      getPeriodBytes();

  /**
   * <pre>
   *产品物料编码
   * </pre>
   *
   * <code>string part_number = 3;</code>
   * @return The partNumber.
   */
  java.lang.String getPartNumber();
  /**
   * <pre>
   *产品物料编码
   * </pre>
   *
   * <code>string part_number = 3;</code>
   * @return The bytes for partNumber.
   */
  com.google.protobuf.ByteString
      getPartNumberBytes();

  /**
   * <pre>
   *产品名称
   * </pre>
   *
   * <code>string product_name = 4;</code>
   * @return The productName.
   */
  java.lang.String getProductName();
  /**
   * <pre>
   *产品名称
   * </pre>
   *
   * <code>string product_name = 4;</code>
   * @return The bytes for productName.
   */
  com.google.protobuf.ByteString
      getProductNameBytes();

  /**
   * <pre>
   *需求数量
   * </pre>
   *
   * <code>int32 quantity = 5;</code>
   * @return The quantity.
   */
  int getQuantity();

  /**
   * <pre>
   *平台
   * </pre>
   *
   * <code>string model = 6;</code>
   * @return The model.
   */
  java.lang.String getModel();
  /**
   * <pre>
   *平台
   * </pre>
   *
   * <code>string model = 6;</code>
   * @return The bytes for model.
   */
  com.google.protobuf.ByteString
      getModelBytes();
}
