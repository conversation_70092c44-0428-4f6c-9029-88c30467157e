package com.github.cret.web.lineLoad.document;

import org.springframework.data.mongodb.core.mapping.Field;

public class PlanDocument {

    //上传日期
    @Field(name = "upload_date")
    private String uploadDate;
    //产品物料编码
    @Field(name = "part_number")
    private String partNumber;
    //产品名称
    @Field(name = "product_name")
    private String productName;
    //平台
    @Field(name = "model")
    private String model;
    //产线编号
    @Field(name = "line")
    private String line;
    //订单周期（年月或周） eg. 2025-01
    @Field(name = "order_period")
    private String orderPeriod;

    //排产所在周期(年月或周) eg. 2025-01
    @Field(name = "production_period")
    private String productionPeriod;
    //生产数量
    @Field(name = "quantity")
    private Integer quantity;
    //消耗时间
    @Field(name = "cost_time")
    private String costTime;


    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getUploadDate() {
        return uploadDate;
    }

    public void setUploadDate(String uploadDate) {
        this.uploadDate = uploadDate;
    }

    public String getPartNumber() {
        return partNumber;
    }

    public void setPartNumber(String partNumber) {
        this.partNumber = partNumber;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getLine() {
        return line;
    }

    public void setLine(String line) {
        this.line = line;
    }

    public String getOrderPeriod() {
        return orderPeriod;
    }

    public void setOrderPeriod(String orderPeriod) {
        this.orderPeriod = orderPeriod;
    }

    public String getProductionPeriod() {
        return productionPeriod;
    }

    public void setProductionPeriod(String productionPeriod) {
        this.productionPeriod = productionPeriod;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getCostTime() {
        return costTime;
    }

    public void setCostTime(String costTime) {
        this.costTime = costTime;
    }

}
