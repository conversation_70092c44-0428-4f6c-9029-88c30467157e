package com.github.cret.web.lineLoad.service.impl;


import com.github.cret.web.lineLoad.document.TempCtMaintenance;
import com.github.cret.web.lineLoad.repository.CtMaintenanceRepository;
import com.github.cret.web.lineLoad.repository.TempCtMaintenanceRepository;
import com.github.cret.web.lineLoad.service.TempCtMaintenanceService;
import com.github.cret.web.security.util.SecurityUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;


@Service
public class TempCtMaintenanceServiceImpl implements TempCtMaintenanceService {

    @Resource
    private TempCtMaintenanceRepository tempCtMaintenanceRepository;
    @Resource
    private CtMaintenanceRepository ctMaintenanceRepository;

    @Override
    public Boolean checkExist(TempCtMaintenance tempCtMaintenance) {
        Long count = ctMaintenanceRepository.countByPartNumberAndLine(tempCtMaintenance.getPartNumber(), tempCtMaintenance.getLine()).orElse(0L);
        if (count > 0) {
            return true;
        }
        TempCtMaintenance exist = tempCtMaintenanceRepository.findByPartNumberAndLine(tempCtMaintenance.getPartNumber(), tempCtMaintenance.getLine()).orElse(null);
        return exist != null && (tempCtMaintenance.getId() == null || !exist.getId().equals(tempCtMaintenance.getId()));
    }

    @Override
    public void save(TempCtMaintenance tempCtMaintenance) {
        Date now = new Date();
        String userName = Objects.requireNonNull(SecurityUtil.getCurrentUser()).name();
        tempCtMaintenance.setCreateTime(now);
        tempCtMaintenance.setUpdateTime(now);
        tempCtMaintenance.setCreator(userName);
        tempCtMaintenance.setUpdater(userName);
        tempCtMaintenanceRepository.save(tempCtMaintenance);
    }

    @Override
    public void deleteById(String id) {
        tempCtMaintenanceRepository.deleteById(id);
    }

    @Override
    public TempCtMaintenance findById(String id) {
        return tempCtMaintenanceRepository.findById(id).get();
    }

    @Override
    public List<TempCtMaintenance> list() {
        return tempCtMaintenanceRepository.findAll();
    }

}

