syntax = "proto3";

option java_multiple_files = true;
option java_package = "com.github.cret.web.lineLoad.grpc.generate";

package com.hongjingwh.lp;

message Order{
  //订单ID
  string id = 1;
  //订单周期（年月或周） eg.  2025-01
  string period = 2;
  //产品物料编码
  string part_number = 3;
  //产品名称
  string product_name = 4;
  //需求数量
  int32 quantity = 5;
  //平台
  string model = 6;
}

message Ct{
  //产品物料编码
  string part_number = 1;
  //产线编号
  string line = 2;
  //生产cycle time，单位 秒
  string ct = 3;
}

message ProductionTime{
  //产线编号
  string line = 1;
  //生产周期 （年月或周） eg. 2025-01
  string period = 2;
  //可用生产时间
  int32  time = 3;
}

message Plan{
  //产品物料编码
  string part_number = 1;
  //产品名称
  string product_name = 2;
  //产线编号
  string line = 3;
  //订单周期（年月或周） eg. 2025-01
  string order_period = 4;
  //排产所在周期(年月或周) eg. 2025-01
  string production_period = 5;
  //生产数量
  int32 quantity = 6;
  //消耗时间
  string cost_time = 7;
  //平台
  string model = 8;
}

message LpRequest{
  repeated Order orders = 1;
  repeated Ct cts = 2;
  repeated ProductionTime production_times = 3;
}
message LpResponse{
  repeated Plan plans = 1;
}

service LpService{
  rpc lp(LpRequest) returns (LpResponse);
}