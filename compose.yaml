services:
  backend:
    image: 192.168.2.201:3000/line_load_predict/line-load_backend
    ports:
     - "8091:8091"
    environment:
      - MONGO_URL=***********************************************
      - WX_WORK_SEND_TEXT_MSG_URL=http://192.168.1.48:9997/wx-server/sendTextCardMsg/cost-analysis
      - WX_WORK_AGENT_ID=1000219
    volumes:
     - /etc/localtime:/etc/localtime:ro
     - ./data/logs:/usr/local/logs
    restart: unless-stopped
  frontend:
    image: 192.168.2.201:3000/line_load_predict/line-load_frontend:latest
    ports:
     - "18092:80"
    volumes:
     - /etc/localtime:/etc/localtime:ro
    restart: unless-stopped
  lp-service:
    image: 192.168.2.201:3000/line_load_predict/lp-service:latest
    restart: unless-stopped