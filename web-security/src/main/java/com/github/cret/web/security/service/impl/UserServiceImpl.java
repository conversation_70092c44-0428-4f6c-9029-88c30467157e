package com.github.cret.web.security.service.impl;

import java.util.HashSet;
import java.util.Optional;

import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.security.document.User;
import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.domain.UserCreate;
import com.github.cret.web.security.domain.UserQuery;
import com.github.cret.web.security.domain.UserUpdate;
import com.github.cret.web.security.domain.UserView;
import com.github.cret.web.security.mapper.UserMapper;
import com.github.cret.web.security.repository.UserRepository;
import com.github.cret.web.security.service.DigestService;
import com.github.cret.web.security.service.UserService;
import com.github.cret.web.security.util.SecurityUtil;

@Service
public class UserServiceImpl implements UserService {

    private final UserRepository repository;
    private final UserMapper userMapper;
    private final MongoTemplate mongoTemplate;
    private final DigestService digestService;

    public UserServiceImpl(UserRepository repository, UserMapper userMapper, MongoTemplate mongoTemplate,
            DigestService digestService) {
        this.repository = repository;
        this.userMapper = userMapper;
        this.mongoTemplate = mongoTemplate;
        this.digestService = digestService;
    }

    @Override
    public AuthUser findAuthUserByUsername(String username) {
        Optional<User> userOp = repository.findFirstByName(username);
        if (userOp.isEmpty()) {
            throw SysErrEnum.NOT_FOUND.exception();
        } else {
            return userMapper.toAuthUser(userOp.get());
        }
    }

    @Override
    public void create(@NotNull UserCreate userCreate) {
        if (repository.existsByName(userCreate.name())) {
            throw SysErrEnum.CONFLICT_KEY.exception("用户名已存在");
        }
        User user = userMapper.toEntity(userCreate);
        user.setPassword(digestService.digestPassword(userCreate.password()));
        user.setRoles(new HashSet<>());
        user.setLocked(false);
        repository.save(user);
    }

    @Override
    public void lock(String id) {
        mongoTemplate.findAndModify(
                Query.query(Criteria.where("_id").is(id)),
                Update.update("locked", true), User.class);
    }

    @Override
    public PageList<UserView> page(PageableParam<UserQuery> param) {
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withIgnoreNullValues()
                .withMatcher("name", ExampleMatcher.GenericPropertyMatchers.startsWith())
                .withMatcher("displayName", ExampleMatcher.GenericPropertyMatchers.startsWith());
        Example<User> example = Example.of(userMapper.toEntity(param.getSearchParams()), matcher);
        return PageList.fromPage(repository.findAll(example, param.getPageRequest()).map(userMapper::toView));
    }

    @Override
    public void update(String id, UserUpdate param) {
        Optional<User> userOp = repository.findById(id);
        if (userOp.isPresent()) {
            User user = userOp.get();
            userMapper.partialUpdate(param, user);
            repository.save(user);
        } else {
            throw SysErrEnum.NOT_FOUND.exception("无此用户");
        }
    }

    @Override
    public void updatePassword(String oldPassword, String newPassword) {
        Optional<User> userOp = repository.findById(SecurityUtil.getCurrentUser().id());
        if (userOp.isPresent()) {
            User user = userOp.get();

            if (!digestService.digestPassword(oldPassword).equals(user.getPassword())) {
                throw SysErrEnum.UNACCEPTABLE_OPERATION.exception("旧密码错误");
            }
            user.setPassword(digestService.digestPassword(newPassword));
            repository.save(user);
        } else {
            throw SysErrEnum.NOT_FOUND.exception("无此用户");
        }
    }

    @Override
    public void resetPassword(String id, String password) {
        Optional<User> userOp = repository.findById(SecurityUtil.getCurrentUser().id());
        if (userOp.isPresent()) {
            User user = userOp.get();
            user.setPassword(digestService.digestPassword(password));
            repository.save(user);
        } else {
            throw SysErrEnum.NOT_FOUND.exception("无此用户");
        }
    }

    @Override
    public UserView get(String id) {
        Optional<User> userOp = repository.findById(id);
        if (userOp.isPresent()) {
            User user = userOp.get();
            return userMapper.toView(user);
        } else {
            throw SysErrEnum.NOT_FOUND.exception("无此用户");
        }
    }

}
