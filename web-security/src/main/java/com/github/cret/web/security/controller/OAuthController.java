package com.github.cret.web.security.controller;

import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.security.domain.LoginRes;
import com.github.cret.web.security.service.AuthService;
import com.github.cret.web.security.service.OAuthService;

@RestController
@ConditionalOnBean(OAuthService.class)
@RequestMapping("/oauth")
public class OAuthController {

    private final OAuthService oAuthService;

    private final AuthService authService;

    public OAuthController(OAuthService oAuthService, AuthService authService) {
        this.oAuthService = oAuthService;
        this.authService = authService;
    }

    @PostMapping("/auth")
    LoginRes auth(@RequestParam String code, @RequestParam String state) {
        String name = oAuthService.auth(code, state);
        return authService.loginByUsername(name);
    }

}
