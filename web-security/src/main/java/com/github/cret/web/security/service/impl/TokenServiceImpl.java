package com.github.cret.web.security.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import com.github.cret.web.security.config.JwtProperties;
import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.service.TokenService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.JwtParser;
import io.jsonwebtoken.Jwts;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;

import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

@Service
@EnableConfigurationProperties(JwtProperties.class)
public class TokenServiceImpl implements TokenService {

    private final JwtProperties properties;
    private final PrivateKey privateKey;
    private final PublicKey publicKey;
    private final JwtParser parser;

    public TokenServiceImpl(JwtProperties properties) throws NoSuchAlgorithmException, InvalidKeySpecException {
        this.properties = properties;
        this.privateKey = getPrivateKey();
        this.publicKey = getPublicKey();
        this.parser = Jwts.parser().verifyWith(publicKey).build();
    }

    private PrivateKey getPrivateKey() throws NoSuchAlgorithmException, InvalidKeySpecException {
        PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(Base64.decode(properties.getPrivateKey()));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePrivate(spec);
    }

    private PublicKey getPublicKey() throws NoSuchAlgorithmException, InvalidKeySpecException {
        X509EncodedKeySpec spec = new X509EncodedKeySpec(Base64.decode(properties.getPublicKey()));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(spec);
    }

    @Override
    public String issueAccessToken(AuthUser user) {
        return issueToken(user, properties.getAccessTokenExpires());
    }

    @Override
    public String issueRefreshToken(AuthUser user) {
        return issueToken(user, properties.getRefreshTokenExpires());

    }

    private String issueToken(AuthUser user, Integer expireSeconds) {
        Date now = new Date();
        Date expires = DateUtil.offsetSecond(new Date(), expireSeconds);
        return Jwts.builder().subject(user.id())
                .claim("name", user.name())
                .claim("displayName", user.displayName())
                .claim("perms", user.perms())
                .claim("avatar", user.avatar())
                .claim("deptId", user.deptId())
                .claim("deptName", user.deptName())
                .claim("locked", user.locked())
                .claim("isAdmin", user.isAdmin())
                .issuedAt(now)
                .expiration(expires)
                .signWith(privateKey)
                .compact();
    }

    @SuppressWarnings("unchecked")
    @Override
    public AuthUser validateAndParse(String token) throws JwtException, IllegalArgumentException {
        Claims claims = parser.parseSignedClaims(token).getPayload();
        return new AuthUser(
                claims.getSubject(),
                claims.get("name").toString(),
                "*",
                claims.get("displayName").toString(),
                new HashSet<String>(claims.get("perms", List.class)),
                claims.get("avatar", String.class),
                claims.get("deptId", String.class),
                claims.get("deptName", String.class),
                claims.get("locked", Boolean.class),
                claims.get("isAdmin", Boolean.class));
    }

    @Override
    public String refresh(String refreshToken) {
        AuthUser authUser = validateAndParse(refreshToken);
        return issueAccessToken(authUser);
    }

}
