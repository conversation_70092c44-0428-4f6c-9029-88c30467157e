package com.github.cret.web.security.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.cret.web.security.domain.AuthUser;
import io.jsonwebtoken.security.InvalidKeyException;

public interface TokenService {

    /**
     * sign a jwt accessToken
     *
     * @param user jwt subject
     * @return
     * @throws JsonProcessingException
     * @throws InvalidKeyException
     */
    String issueAccessToken(AuthUser user) throws InvalidKeyException, JsonProcessingException;

    /**
     * sign a jwt refreshToken
     *
     * @param user jwt subject
     * @return
     */
    String issueRefreshToken(AuthUser user);

    /**
     * validate and parse jwt token
     *
     * @param token
     * @return
     */
    AuthUser validateAndParse(String token);

    /***
     * get accessToken by refreshToken
     *
     * @param refreshToken
     * @return
     */
    String refresh(String refreshToken);

}
