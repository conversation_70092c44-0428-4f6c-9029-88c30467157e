package com.github.cret.web.security.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.domain.LoginRes;
import com.github.cret.web.security.service.AuthService;

@RestController
@RequestMapping("/auth")
public class AuthController {

    private final AuthService authService;

    public AuthController(AuthService authService) {
        this.authService = authService;
    }

    @PostMapping("/login")
    public LoginRes loginByPassword(@RequestParam String username, @RequestParam String password) {
        return authService.loginByPassword(username, password);
    }

    @GetMapping("/userInfo")
    public AuthUser getUserInfo() {
        return authService.getUserInfo();
    }

    @PostMapping("/refresh")
    public void refreshToken() {
        return;
    }
}
