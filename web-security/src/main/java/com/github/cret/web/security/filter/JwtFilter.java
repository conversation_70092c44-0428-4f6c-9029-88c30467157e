package com.github.cret.web.security.filter;

import java.io.IOException;
import java.util.List;

import org.springframework.http.HttpHeaders;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.service.TokenService;

import io.jsonwebtoken.JwtException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@Component
public class JwtFilter extends OncePerRequestFilter {

    private final TokenService tokenService;

    // Use constants for clarity and maintainability
    private static final String TOKEN_REFRESH_HEADER = "Token-Refresh";
    private static final String TOKEN_PREFIX = "Bearer ";

    public JwtFilter(TokenService tokenService) {
        this.tokenService = tokenService;
    }

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
            @NonNull FilterChain chain) throws ServletException, IOException {

        // Extract and validate the Authorization header early
        final String authorizationHeader = request.getHeader(HttpHeaders.AUTHORIZATION);
        if (!StringUtils.hasText(authorizationHeader) || !authorizationHeader.startsWith(TOKEN_PREFIX)) {
            chain.doFilter(request, response);
            return;
        }

        try {
            // Extract the JWT and validate it
            String accessToken = authorizationHeader.substring(TOKEN_PREFIX.length());
            AuthUser authUser = tokenService.validateAndParse(accessToken);

            // Set authentication in SecurityContext
            setAuthenticationContext(request, authUser);

            // Handle token refresh if requested
            handleTokenRefresh(request, response, accessToken);

            // Continue the filter chain
            chain.doFilter(request, response);

        } catch (JwtException ex) {
            // Log the exception and continue the filter chain, allowing subsequent
            // filters/handlers to manage errors
            logger.debug("JWT authentication failed for request: " + request.getRequestURI(), ex);
            chain.doFilter(request, response);
        } catch (Exception ex) {
            // Catch any other unexpected exceptions during the process
            logger.error("An unexpected error occurred during JWT processing for request: " + request.getRequestURI(),
                    ex);
            chain.doFilter(request, response);
        }
    }

    /**
     * Sets the authenticated user in the SecurityContext.
     * 
     * @param request  The current HttpServletRequest.
     * @param authUser The authenticated user details.
     */
    private void setAuthenticationContext(HttpServletRequest request, AuthUser authUser) {
        List<GrantedAuthority> authorities = AuthorityUtils.createAuthorityList(authUser.perms());

        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                authUser, // Principal
                null, // Credentials (not needed for JWT authentication)
                authorities);

        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }

    /**
     * Handles the refreshing of the access token if the "Token-Refresh" header is
     * present.
     * 
     * @param request            The current HttpServletRequest.
     * @param response           The current HttpServletResponse.
     * @param currentAccessToken The current access token.
     */
    private void handleTokenRefresh(HttpServletRequest request, HttpServletResponse response,
            String currentAccessToken) {
        if (StringUtils.hasText(request.getHeader(TOKEN_REFRESH_HEADER))) {
            try {
                String newAccessToken = tokenService.refresh(currentAccessToken);
                response.addHeader(TOKEN_REFRESH_HEADER, newAccessToken);
            } catch (JwtException ex) {
                // Log but don't prevent the request from proceeding if refresh fails
                logger.warn("Failed to refresh token for request: " + request.getRequestURI(), ex);
            }
        }
    }
}