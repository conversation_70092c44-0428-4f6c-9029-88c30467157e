package com.github.cret.web.security.util;

import com.github.cret.web.security.domain.AuthUser;
import org.springframework.security.core.context.SecurityContextHolder;

public class SecurityUtil {
    public static AuthUser getCurrentUser() {
        return SecurityContextHolder.getContext().getAuthentication()
                .getPrincipal() instanceof AuthUser ? (AuthUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal() : null;
    }

}
