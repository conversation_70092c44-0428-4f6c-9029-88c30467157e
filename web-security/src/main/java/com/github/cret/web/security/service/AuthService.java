package com.github.cret.web.security.service;

import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.domain.LoginRes;

public interface AuthService {
    /***
     * login use password
     * 
     * @param username name
     * @param password password
     * @return token and user info
     */
    LoginRes loginByPassword(String username, String password);

    /***
     * login only by name
     * used for oauth login
     * 
     * @param username name
     * @return token and user info
     */
    LoginRes loginByUsername(String username);

    /**
     * get user info by accessToken
     *
     * @return user info
     */
    AuthUser getUserInfo();

    /**
     * use refreshToken to refresh accessToken
     *
     * @param refreshToken refreshToken
     * @return accessToken
     */
    String refreshToken(String refreshToken);
}
