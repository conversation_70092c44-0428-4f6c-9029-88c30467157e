package com.github.cret.web.security.controller;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.security.domain.UpdatePasswordParam;
import com.github.cret.web.security.domain.UserCreate;
import com.github.cret.web.security.domain.UserQuery;
import com.github.cret.web.security.domain.UserUpdate;
import com.github.cret.web.security.domain.UserView;
import com.github.cret.web.security.service.UserService;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/user")
public class UserController {

    private final UserService userService;

    public UserController(UserService userService) {
        this.userService = userService;
    }

    @PostMapping("/create")
    @ResponseBody
    @PreAuthorize("hasAuthority('ADMIN')")
    void create(@RequestBody @Valid UserCreate userCreate) {
        userService.create(userCreate);
    }

    @PostMapping("/page")
    @ResponseBody
    @PreAuthorize("hasAuthority('ADMIN')")
    PageList<UserView> page(@RequestBody PageableParam<UserQuery> param) {
        return userService.page(param);
    }

    @PostMapping("/resetPassword/{id}")
    @ResponseBody
    @PreAuthorize("hasAuthority('ADMIN')")
    public void resetPassword(@PathVariable("id") String id, String newPassword) {
        userService.resetPassword(id, newPassword);
    }

    @PostMapping("/lock/{id}")
    @ResponseBody
    @PreAuthorize("hasAuthority('ADMIN')")
    public void lock(@PathVariable String id) {
        userService.lock(id);
    }

    @GetMapping("/{id}")
    public UserView get(@PathVariable String id) {
        return userService.get(id);
    }

    @PostMapping("/update/{id}")
    @PreAuthorize("hasAuthority('ADMIN')")
    public void update(@PathVariable String id, @RequestBody @Valid UserUpdate userUpdate) {
        userService.update(id, userUpdate);
    }

    @PostMapping("/updatePassword")
    public void updatePassword(@RequestBody UpdatePasswordParam param) {
        userService.updatePassword(param.oldPassword(), param.newPassword());
    }

}
