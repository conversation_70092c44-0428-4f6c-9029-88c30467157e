package com.github.cret.web.security.domain;

import java.util.Set;

import org.springframework.boot.context.properties.bind.DefaultValue;

import jakarta.validation.constraints.NotBlank;

public record UserCreate(
        @NotBlank String name,
        @NotBlank String displayName,
        String avatar,
        @NotBlank String password,
        @DefaultValue("false") Boolean isAdmin,
        Set<String> perms) {

}
