package com.github.cret.web.security.service;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.domain.UserCreate;
import com.github.cret.web.security.domain.UserQuery;
import com.github.cret.web.security.domain.UserUpdate;
import com.github.cret.web.security.domain.UserView;

public interface UserService {

    /**
     * get AuthUser by name
     * TODO throw exception
     *
     * @param username
     * @return
     */
    AuthUser findAuthUserByUsername(String username);

    void create(UserCreate userCreate);

    void lock(String id);

    PageList<UserView> page(PageableParam<UserQuery> param);

    void update(String id, UserUpdate param);

    void updatePassword(String oldPassword, String newPassword);

    void resetPassword(String id, String password);

    UserView get(String id);
}
