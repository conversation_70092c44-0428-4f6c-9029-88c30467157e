package com.github.cret.web.security.service.impl;

import org.casbin.casdoor.service.AuthService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;

import com.github.cret.web.security.config.CasdoorProperties;
import com.github.cret.web.security.service.OAuthService;

@Service
@EnableConfigurationProperties(CasdoorProperties.class)
@ConditionalOnProperty(name = "casdoor.endpoint")
public class CasdoorOAuthServiceImpl implements OAuthService {

    private final AuthService authService;

    public CasdoorOAuthServiceImpl(CasdoorProperties properties) {
        this.authService = new AuthService(properties);

    }

    @Override
    public String auth(String code, String state) {
        String token = authService.getOAuthToken(code, state);
        return authService.parseJwtToken(token).name;

    }

}
