package com.github.cret.web.system.service;

import java.util.List;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.system.document.MenuItem;
import com.github.cret.web.system.domain.MenuItemCreate;

public interface MenuService {

    List<MenuItem> getUserMenu();

    List<MenuItem> listItem(MenuItem param);

    List<MenuItem> listChildren(String parentId);

    List<MenuItem> listRootItem();

    PageList<MenuItem> pageItem(PageableParam<MenuItem> param);

    void create(MenuItemCreate param);

    void delete(String id);

    void update(String id, MenuItem menuItem);

    MenuItem get(String id);
}
