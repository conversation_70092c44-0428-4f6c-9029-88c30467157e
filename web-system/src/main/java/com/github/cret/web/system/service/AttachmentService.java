package com.github.cret.web.system.service;

import java.io.IOException;
import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.github.cret.web.common.domain.DownloadFile;
import com.github.cret.web.common.domain.FileRef;

public interface AttachmentService {
    FileRef upload(MultipartFile file) throws IOException;

    List<FileRef> uploadMulti(List<MultipartFile> files);

    DownloadFile download(String key) throws IOException;

    void del(String key) throws IOException;
}
