package com.github.cret.web.system.domain;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.springframework.boot.context.properties.bind.DefaultValue;

public record MenuItemCreate(String title,
                             String icon,
                             String target,
                             @DefaultValue("")
                             String access,
                             String parentId,
                             @NotBlank
                             String path,
                             @NotNull
                             Integer order,
                             String url,
                             String redirect) {

}
