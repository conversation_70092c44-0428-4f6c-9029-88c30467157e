package com.github.cret.web.system.document;

import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import com.github.cret.web.system.domain.DictItem;

@Document("sys_dict")
public class Dict {

    @Id
    private String id;

    private String code;

    private String name;

    private List<DictItem> itemList;

    public Dict() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public List<DictItem> getItemList() {
        return itemList;
    }

    public void setItemList(List<DictItem> itemList) {
        this.itemList = itemList;
    }

    public void setName(String name) {
        this.name = name;
    }
}
