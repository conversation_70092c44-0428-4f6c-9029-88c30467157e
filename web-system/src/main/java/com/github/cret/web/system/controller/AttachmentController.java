package com.github.cret.web.system.controller;

import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.Charset;

import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.HandlerMapping;

import com.github.cret.web.common.domain.DownloadFile;
import com.github.cret.web.common.domain.FileRef;
import com.github.cret.web.system.service.AttachmentService;

import jakarta.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/attachment")
public class AttachmentController {

    private final AttachmentService service;

    public AttachmentController(AttachmentService service) {
        this.service = service;
    }

    @PostMapping("")
    FileRef upload(@RequestParam MultipartFile file) throws IOException {
        return service.upload(file);
    }

    @GetMapping("/**")
    public ResponseEntity<Resource> download(HttpServletRequest request) throws IOException {
        String restOfTheUrl = (String) request.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE);
        String key = URLDecoder.decode(restOfTheUrl.substring("/attachment/".length()), Charset.forName("UTF-8"));
        HttpHeaders headers = new HttpHeaders();
        DownloadFile file = service.download(key);
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + file.name());
        headers.add(HttpHeaders.CONTENT_TYPE, file.contentType());
        return ResponseEntity.ok().headers(headers).body(file.resource());
    }

    @DeleteMapping("/**")
    public void del(HttpServletRequest request) throws IOException {
        String restOfTheUrl = (String) request.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE);
        String key = URLDecoder.decode(restOfTheUrl.substring("/attachment/".length()), Charset.forName("UTF-8"));
        service.del(key);
    }

}
