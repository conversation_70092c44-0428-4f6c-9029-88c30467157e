package com.github.cret.web.system.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.cret.web.security.domain.UserCreate;
import com.github.cret.web.security.service.UserService;
import com.github.cret.web.system.document.Dict;
import com.github.cret.web.system.document.MenuItem;
import com.github.cret.web.system.domain.DictItem;
import com.github.cret.web.system.service.InitService;

import cn.hutool.core.collection.ListUtil;

@Service
public class InitServiceImpl implements InitService, InitializingBean {

    private final MongoTemplate mongoTemplate;
    private final UserService userService;

    public InitServiceImpl(MongoTemplate mongoTemplate, UserService userService) {
        this.mongoTemplate = mongoTemplate;
        this.userService = userService;
    }

    @Override
    @Transactional
    public void afterPropertiesSet() throws Exception {
        init();
    }

    @Override
    public void init() {
        if (!mongoTemplate.collectionExists("sys_flag")) {
            initMenu();
            initAdmin();
            initDict();
            initIndex();
            mongoTemplate.createCollection("sys_flag");
        }
    }

    private void initMenu() {
        MenuItem adminMenu = new MenuItem();
        adminMenu.setTitle("系统管理");
        adminMenu.setPath("/admin");
        adminMenu.setAccess("ADMIN");
        MenuItem dictMenu = new MenuItem();
        dictMenu.setAccess("ADMIN");
        dictMenu.setPath("/admin/dict");
        dictMenu.setTitle("字典管理");
        MenuItem menuMenu = new MenuItem();
        menuMenu.setTitle("菜单管理");
        menuMenu.setAccess("ADMIN");
        menuMenu.setPath("/admin/menu");
        MenuItem userMenu = new MenuItem();
        userMenu.setTitle("用户管理");
        userMenu.setAccess("ADMIN");
        userMenu.setPath("/admin/user");

        adminMenu = mongoTemplate.insert(adminMenu);
        dictMenu.setParentId(adminMenu.getId());
        menuMenu.setParentId(adminMenu.getId());
        userMenu.setParentId(adminMenu.getId());
        List<MenuItem> menuItemList = List.of(dictMenu, menuMenu, userMenu);
        mongoTemplate.insertAll(menuItemList);
    }

    private void initDict() {
        Dict targetDict = new Dict();
        targetDict.setCode("SYS_TARGET");
        targetDict.setName("target");
        DictItem _self = new DictItem();
        _self.setLabel("_self");
        _self.setValue("_self");
        DictItem _blank = new DictItem();
        _blank.setLabel("_blank");
        _blank.setValue("_blank");
        DictItem _parent = new DictItem();
        _parent.setLabel("_parent");
        _parent.setValue("_parent");
        List<DictItem> targetItemList = new ArrayList<>();
        targetItemList.add(_self);
        targetItemList.add(_blank);
        targetItemList.add(_parent);
        targetDict.setItemList(targetItemList);

        Dict permDict = new Dict();
        permDict.setCode("SYS_PERM");
        permDict.setName("权限");
        DictItem adminPerm = new DictItem();
        adminPerm.setLabel("管理员");
        adminPerm.setValue("ADMIN");
        permDict.setItemList(ListUtil.of(adminPerm));
        mongoTemplate.insertAll(ListUtil.of(targetDict, permDict));
    }

    private void initAdmin() {
        HashSet<String> perms = new HashSet<>();
        perms.add("ADMIN");
        UserCreate userCreate = new UserCreate("admin", "管理员", "", "123456", true, perms);
        userService.create(userCreate);
    }

    private void initIndex() {
        mongoTemplate.indexOps("sys_user").createIndex(
                new Index("name", Direction.ASC));
        mongoTemplate.indexOps("sys_dict").createIndex(
                new Index("code", Direction.ASC));
        mongoTemplate.indexOps("sys_menu_item").createIndex(
                new Index("path", Direction.ASC));
    }

}
