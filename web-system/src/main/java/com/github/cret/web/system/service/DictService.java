package com.github.cret.web.system.service;

import java.util.List;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.system.document.Dict;
import com.github.cret.web.system.domain.DictCreate;
import com.github.cret.web.system.domain.DictItem;
import com.github.cret.web.system.domain.DictSimple;
import com.github.cret.web.system.domain.DictUpdate;

public interface DictService {

    Dict getByCode(String code);

    PageList<DictSimple> page(PageableParam<DictSimple> param);

    void create(DictCreate dictCreate);

    void delete(String id);

    Dict get(String id);

    void update(String id, DictUpdate dictUpdate);

    List<DictItem> listItem(String dictCode);
}
