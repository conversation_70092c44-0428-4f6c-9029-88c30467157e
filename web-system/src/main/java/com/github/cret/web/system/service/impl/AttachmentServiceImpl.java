package com.github.cret.web.system.service.impl;

import java.io.IOException;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.github.cret.web.common.component.FileStore;
import com.github.cret.web.common.domain.DownloadFile;
import com.github.cret.web.common.domain.FileRef;
import com.github.cret.web.system.service.AttachmentService;

import cn.hutool.core.lang.UUID;

@Service
public class AttachmentServiceImpl implements AttachmentService {

    private final FileStore fileStore;

    public AttachmentServiceImpl(FileStore fileStore) {
        this.fileStore = fileStore;
    }

    @Override
    public FileRef upload(MultipartFile file) throws IOException {
        String key = UUID.fastUUID() + "/" + file.getOriginalFilename();
        fileStore.set(key, file);
        return new FileRef(key, file.getOriginalFilename(), file.getSize());
    }

    @Override
    public List<FileRef> uploadMulti(List<MultipartFile> files) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'uploadMulti'");
    }

    @Override
    public DownloadFile download(String key) throws IOException {
        return fileStore.get(key);
    }

    @Override
    public void del(String key) throws IOException {
        fileStore.del(key);
    }

}
